// Events Management System

// Initialize events data when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeEventsData();
    loadEmployeeOptions();
    setupEventListeners();
    displayEvents();
    updateEventsStatistics();
    setCurrentDate();
});

// Initialize events data
function initializeEventsData() {
    // Initialize events if not exists
    if (!localStorage.getItem('events')) {
        localStorage.setItem('events', JSON.stringify([]));
    }

    // Initialize event RSVPs if not exists
    if (!localStorage.getItem('eventRSVPs')) {
        localStorage.setItem('eventRSVPs', JSON.stringify([]));
    }
}

// Load employee options for dropdowns
function loadEmployeeOptions() {
    const employees = db.getEmployees();

    // Load organizer options
    const organizerSelect = document.getElementById('eventOrganizer');
    if (organizerSelect) {
        organizerSelect.innerHTML = '<option value="">اختر المنظم</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            organizerSelect.appendChild(option);
        });
    }

    // Load RSVP employee options
    const rsvpEmployeeSelect = document.getElementById('rsvpEmployee');
    if (rsvpEmployeeSelect) {
        rsvpEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            rsvpEmployeeSelect.appendChild(option);
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Set minimum date to today
    const eventDateInput = document.getElementById('eventDate');
    if (eventDateInput) {
        const today = new Date().toISOString().split('T')[0];
        eventDateInput.min = today;
    }
}

// Set current date
function setCurrentDate() {
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const today = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        currentDateElement.textContent = today.toLocaleDateString('ar-DZ', options);
    }
}

// Save event
function saveEvent() {
    const eventName = document.getElementById('eventName').value.trim();
    const eventType = document.getElementById('eventType').value;
    const eventDate = document.getElementById('eventDate').value;
    const eventTime = document.getElementById('eventTime').value;
    const duration = parseFloat(document.getElementById('eventDuration').value) || 0;
    const venue = document.getElementById('eventVenue').value.trim();
    const capacity = parseInt(document.getElementById('eventCapacity').value);
    const budget = parseFloat(document.getElementById('eventBudget').value) || 0;
    const organizerId = document.getElementById('eventOrganizer').value;
    const category = document.getElementById('eventCategory').value;
    const status = document.getElementById('eventStatus').value;
    const requiresRSVP = document.getElementById('eventRequiresRSVP').value === 'true';
    const description = document.getElementById('eventDescription').value.trim();
    const program = document.getElementById('eventProgram').value.trim();
    const requirements = document.getElementById('eventRequirements').value.trim();
    const notes = document.getElementById('eventNotes').value.trim();

    // Validation
    if (!eventName || !eventType || !eventDate || !eventTime || !venue || !capacity || !organizerId || !description) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (capacity <= 0) {
        showAlert('السعة القصوى يجب أن تكون أكبر من صفر', 'error');
        return;
    }

    // Get organizer info
    const organizer = db.getEmployees().find(emp => emp.id == organizerId);
    if (!organizer) {
        showAlert('لم يتم العثور على المنظم', 'error');
        return;
    }

    // Create event object
    const event = {
        id: Date.now(),
        name: eventName,
        type: eventType,
        date: eventDate,
        time: eventTime,
        duration: duration,
        venue: venue,
        capacity: capacity,
        currentAttendees: 0,
        budget: budget,
        organizerId: parseInt(organizerId),
        organizerName: organizer.name,
        category: category,
        status: status,
        requiresRSVP: requiresRSVP,
        description: description,
        program: program,
        requirements: requirements,
        notes: notes,
        createdAt: new Date().toISOString()
    };

    // Save event
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    events.push(event);
    localStorage.setItem('events', JSON.stringify(events));

    showAlert('تم حفظ الفعالية بنجاح', 'success');
    hideModal('addEventModal');
    resetEventForm();
    displayEvents();
    updateEventsStatistics();
}

// Reset event form
function resetEventForm() {
    document.getElementById('addEventForm').reset();
    document.getElementById('eventStatus').value = 'قادمة';
    document.getElementById('eventRequiresRSVP').value = 'false';
}

// Display events
function displayEvents() {
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    const tableBody = document.getElementById('eventsTableBody');

    if (!tableBody) return;

    if (events.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-calendar-alt fa-2x"></i>
                        <h3>لا توجد فعاليات مسجلة</h3>
                        <p>اضغط على "تنظيم فعالية جديدة" لبدء إضافة الفعاليات</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by event date (newest first)
    events.sort((a, b) => new Date(b.date) - new Date(a.date));

    tableBody.innerHTML = events.map(event => {
        const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');
        const eventRSVPs = rsvps.filter(rsvp => rsvp.eventId === event.id);
        const currentAttendees = eventRSVPs.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);

        return `
            <tr>
                <td>
                    <div class="event-info">
                        <strong>${event.name}</strong>
                        <small>${event.organizerName}</small>
                    </div>
                </td>
                <td>
                    <span class="event-type-badge">${event.type}</span>
                </td>
                <td>
                    <div class="event-datetime">
                        <strong>${formatDate(event.date)}</strong>
                        <small>${event.time}</small>
                    </div>
                </td>
                <td>${event.venue}</td>
                <td>
                    <span class="attendees-count">
                        ${currentAttendees}/${event.capacity}
                    </span>
                </td>
                <td>
                    ${event.budget > 0 ? `<strong>${event.budget.toLocaleString()} دج</strong>` : '-'}
                </td>
                <td>
                    <span class="status-badge ${getEventStatusClass(event.status)}">
                        ${event.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewEventDetails(${event.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${event.requiresRSVP && event.status === 'مؤكدة' ? `
                            <button class="btn-icon btn-success" onclick="showRSVPModal(${event.id})" title="تأكيد الحضور">
                                <i class="fas fa-user-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-warning" onclick="editEvent(${event.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteEvent(${event.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get event status class for styling
function getEventStatusClass(status) {
    switch(status) {
        case 'مؤكدة': return 'confirmed';
        case 'جارية': return 'ongoing';
        case 'مكتملة': return 'completed';
        case 'ملغية': return 'cancelled';
        case 'مؤجلة': return 'postponed';
        default: return 'upcoming';
    }
}

// Update events statistics
function updateEventsStatistics() {
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');

    const totalEvents = events.length;
    const today = new Date();
    const upcomingEvents = events.filter(event => new Date(event.date) > today && event.status !== 'ملغية').length;
    const totalAttendees = rsvps.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);
    const totalBudget = events.reduce((sum, event) => sum + (event.budget || 0), 0);

    // Update DOM elements
    const totalEventsEl = document.getElementById('totalEvents');
    const upcomingEventsEl = document.getElementById('upcomingEvents');
    const totalAttendeesEl = document.getElementById('totalAttendees');
    const totalBudgetEl = document.getElementById('totalBudget');

    if (totalEventsEl) totalEventsEl.textContent = totalEvents;
    if (upcomingEventsEl) upcomingEventsEl.textContent = upcomingEvents;
    if (totalAttendeesEl) totalAttendeesEl.textContent = totalAttendees;
    if (totalBudgetEl) totalBudgetEl.textContent = `${totalBudget.toLocaleString()} دج`;
}

// Search events
function searchEvents() {
    const searchTerm = document.getElementById('eventsSearch').value.toLowerCase();
    const events = JSON.parse(localStorage.getItem('events') || '[]');

    const filteredEvents = events.filter(event =>
        event.name.toLowerCase().includes(searchTerm) ||
        event.type.toLowerCase().includes(searchTerm) ||
        event.venue.toLowerCase().includes(searchTerm) ||
        event.description.toLowerCase().includes(searchTerm) ||
        event.organizerName.toLowerCase().includes(searchTerm)
    );

    displayFilteredEvents(filteredEvents);
}

// Filter events
function filterEvents() {
    const filterValue = document.getElementById('eventsFilter').value;
    const events = JSON.parse(localStorage.getItem('events') || '[]');

    let filteredEvents = events;

    if (filterValue) {
        filteredEvents = events.filter(event => event.status === filterValue);
    }

    displayFilteredEvents(filteredEvents);
}

// Display filtered events
function displayFilteredEvents(events) {
    const tableBody = document.getElementById('eventsTableBody');

    if (!tableBody) return;

    if (events.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على فعاليات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by event date (newest first)
    events.sort((a, b) => new Date(b.date) - new Date(a.date));

    tableBody.innerHTML = events.map(event => {
        const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');
        const eventRSVPs = rsvps.filter(rsvp => rsvp.eventId === event.id);
        const currentAttendees = eventRSVPs.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);

        return `
            <tr>
                <td>
                    <div class="event-info">
                        <strong>${event.name}</strong>
                        <small>${event.organizerName}</small>
                    </div>
                </td>
                <td>
                    <span class="event-type-badge">${event.type}</span>
                </td>
                <td>
                    <div class="event-datetime">
                        <strong>${formatDate(event.date)}</strong>
                        <small>${event.time}</small>
                    </div>
                </td>
                <td>${event.venue}</td>
                <td>
                    <span class="attendees-count">
                        ${currentAttendees}/${event.capacity}
                    </span>
                </td>
                <td>
                    ${event.budget > 0 ? `<strong>${event.budget.toLocaleString()} دج</strong>` : '-'}
                </td>
                <td>
                    <span class="status-badge ${getEventStatusClass(event.status)}">
                        ${event.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewEventDetails(${event.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${event.requiresRSVP && event.status === 'مؤكدة' ? `
                            <button class="btn-icon btn-success" onclick="showRSVPModal(${event.id})" title="تأكيد الحضور">
                                <i class="fas fa-user-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-warning" onclick="editEvent(${event.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteEvent(${event.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Format date for display
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

// Show upcoming events
function showUpcomingEvents() {
    document.getElementById('eventsFilter').value = 'قادمة';
    filterEvents();
    showAlert('تم عرض الفعاليات القادمة فقط', 'info');
}

// Show completed events
function showCompletedEvents() {
    document.getElementById('eventsFilter').value = 'مكتملة';
    filterEvents();
    showAlert('تم عرض الفعاليات المكتملة فقط', 'info');
}

// Show event calendar
function showEventCalendar() {
    showAlert('سيتم تطوير التقويم الشهري قريباً', 'info');
}

// Show event types
function showEventTypes() {
    showAlert('سيتم تطوير إدارة أنواع الفعاليات قريباً', 'info');
}

// View event details
function viewEventDetails(eventId) {
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    const event = events.find(e => e.id === eventId);

    if (!event) {
        showAlert('لم يتم العثور على الفعالية', 'error');
        return;
    }

    const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');
    const eventRSVPs = rsvps.filter(rsvp => rsvp.eventId === eventId);
    const currentAttendees = eventRSVPs.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);

    // Fill details modal
    document.getElementById('detailsEventName').textContent = event.name;
    document.getElementById('detailsEventType').textContent = event.type;
    document.getElementById('detailsEventDate').textContent = formatDate(event.date);
    document.getElementById('detailsEventTime').textContent = event.time;
    document.getElementById('detailsEventDuration').textContent = event.duration > 0 ? `${event.duration} ساعة` : '-';
    document.getElementById('detailsEventVenue').textContent = event.venue;
    document.getElementById('detailsEventCapacity').textContent = event.capacity;
    document.getElementById('detailsCurrentAttendees').textContent = currentAttendees;
    document.getElementById('detailsEventBudget').textContent = event.budget > 0 ? `${event.budget.toLocaleString()} دج` : '-';
    document.getElementById('detailsEventOrganizer').textContent = event.organizerName;
    document.getElementById('detailsEventCategory').textContent = event.category || '-';
    document.getElementById('detailsEventStatus').textContent = event.status;
    document.getElementById('detailsEventDescription').textContent = event.description;
    document.getElementById('detailsEventProgram').textContent = event.program || '-';
    document.getElementById('detailsEventRequirements').textContent = event.requirements || '-';
    document.getElementById('detailsEventNotes').textContent = event.notes || '-';

    // Display attendees list
    const attendeesList = document.getElementById('attendeesList');
    if (attendeesList) {
        if (eventRSVPs.length === 0) {
            attendeesList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users fa-2x"></i>
                    <p>لا توجد تأكيدات حضور للفعالية حتى الآن</p>
                </div>
            `;
        } else {
            attendeesList.innerHTML = `
                <div class="attendees-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>المرافقين</th>
                                <th>الهاتف</th>
                                <th>قيود غذائية</th>
                                <th>تاريخ التأكيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${eventRSVPs.map(rsvp => `
                                <tr>
                                    <td><strong>${rsvp.employeeName}</strong></td>
                                    <td>${rsvp.employeeDepartment}</td>
                                    <td>${rsvp.companions || 0}</td>
                                    <td>${rsvp.phone}</td>
                                    <td>${rsvp.dietaryRestrictions || '-'}</td>
                                    <td>${formatDate(rsvp.rsvpDate)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
    }

    showModal('eventDetailsModal');
}

// Show RSVP modal
function showRSVPModal(eventId) {
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    const event = events.find(e => e.id === eventId);

    if (!event) {
        showAlert('لم يتم العثور على الفعالية', 'error');
        return;
    }

    const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');
    const eventRSVPs = rsvps.filter(rsvp => rsvp.eventId === eventId);
    const currentAttendees = eventRSVPs.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);
    const availableSeats = event.capacity - currentAttendees;

    if (availableSeats <= 0) {
        showAlert('عذراً، لا توجد مقاعد متاحة في هذه الفعالية', 'warning');
        return;
    }

    // Check if event is in the past
    const today = new Date();
    const eventDate = new Date(event.date);

    if (eventDate < today) {
        showAlert('عذراً، لا يمكن تأكيد الحضور لفعالية انتهت', 'warning');
        return;
    }

    // Fill RSVP modal
    document.getElementById('rsvpEventId').value = eventId;
    document.getElementById('rsvpEventName').textContent = event.name;
    document.getElementById('rsvpEventDateTime').textContent = `${formatDate(event.date)} - ${event.time}`;
    document.getElementById('rsvpEventVenue').textContent = event.venue;
    document.getElementById('rsvpAvailableSeats').textContent = availableSeats;

    showModal('rsvpModal');
}

// Save RSVP
function saveRSVP() {
    const eventId = parseInt(document.getElementById('rsvpEventId').value);
    const employeeId = document.getElementById('rsvpEmployee').value;
    const companions = parseInt(document.getElementById('rsvpCompanions').value) || 0;
    const phone = document.getElementById('rsvpPhone').value.trim();
    const dietaryRestrictions = document.getElementById('rsvpDietaryRestrictions').value.trim();
    const notes = document.getElementById('rsvpNotes').value.trim();

    if (!employeeId || !phone) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Get employee info
    const employee = db.getEmployees().find(emp => emp.id == employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Get event info
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    const event = events.find(e => e.id === eventId);
    if (!event) {
        showAlert('لم يتم العثور على الفعالية', 'error');
        return;
    }

    // Check available seats
    const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');
    const eventRSVPs = rsvps.filter(rsvp => rsvp.eventId === eventId);
    const currentAttendees = eventRSVPs.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);
    const availableSeats = event.capacity - currentAttendees;
    const requiredSeats = 1 + companions;

    if (requiredSeats > availableSeats) {
        showAlert(`عذراً، المقاعد المتاحة (${availableSeats}) أقل من المطلوب (${requiredSeats})`, 'error');
        return;
    }

    // Check if employee already has RSVP
    const existingRSVP = eventRSVPs.find(rsvp => rsvp.employeeId === parseInt(employeeId));
    if (existingRSVP) {
        showAlert('هذا الموظف أكد حضوره مسبقاً لهذه الفعالية', 'error');
        return;
    }

    // Create RSVP object
    const rsvp = {
        id: Date.now(),
        eventId: eventId,
        employeeId: parseInt(employeeId),
        employeeName: employee.name,
        employeeDepartment: employee.department,
        companions: companions,
        phone: phone,
        dietaryRestrictions: dietaryRestrictions,
        notes: notes,
        rsvpDate: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString()
    };

    // Save RSVP
    rsvps.push(rsvp);
    localStorage.setItem('eventRSVPs', JSON.stringify(rsvps));

    showAlert('تم تأكيد الحضور بنجاح', 'success');
    hideModal('rsvpModal');
    resetRSVPForm();
    displayEvents();
    updateEventsStatistics();
}

// Reset RSVP form
function resetRSVPForm() {
    document.getElementById('rsvpForm').reset();
}

// Edit event
function editEvent(eventId) {
    showAlert('سيتم تطوير ميزة التعديل قريباً', 'info');
}

// Delete event
function deleteEvent(eventId) {
    if (confirm('هل أنت متأكد من حذف هذه الفعالية؟ سيتم حذف جميع تأكيدات الحضور المرتبطة بها أيضاً.')) {
        // Delete event
        const events = JSON.parse(localStorage.getItem('events') || '[]');
        const updatedEvents = events.filter(event => event.id !== eventId);
        localStorage.setItem('events', JSON.stringify(updatedEvents));

        // Delete related RSVPs
        const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');
        const updatedRSVPs = rsvps.filter(rsvp => rsvp.eventId !== eventId);
        localStorage.setItem('eventRSVPs', JSON.stringify(updatedRSVPs));

        showAlert('تم حذف الفعالية بنجاح', 'success');
        displayEvents();
        updateEventsStatistics();
    }
}

// Export events report
function exportEventsReport() {
    const events = JSON.parse(localStorage.getItem('events') || '[]');
    const rsvps = JSON.parse(localStorage.getItem('eventRSVPs') || '[]');

    if (events.length === 0) {
        showAlert('لا توجد فعاليات لتصديرها', 'warning');
        return;
    }

    const totalEvents = events.length;
    const today = new Date();
    const upcomingEvents = events.filter(event => new Date(event.date) > today && event.status !== 'ملغية').length;
    const totalAttendees = rsvps.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);
    const totalBudget = events.reduce((sum, event) => sum + (event.budget || 0), 0);

    let reportContent = `تقرير الفعاليات والحفلات
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

إحصائيات عامة:
- إجمالي الفعاليات: ${totalEvents}
- الفعاليات القادمة: ${upcomingEvents}
- إجمالي الحضور: ${totalAttendees}
- إجمالي الميزانية: ${totalBudget.toLocaleString()} دج

تفاصيل الفعاليات:
`;

    events.forEach((event, index) => {
        const eventRSVPs = rsvps.filter(rsvp => rsvp.eventId === event.id);
        const currentAttendees = eventRSVPs.reduce((sum, rsvp) => sum + 1 + (rsvp.companions || 0), 0);

        reportContent += `
${index + 1}. ${event.name}
   النوع: ${event.type}
   التاريخ: ${formatDate(event.date)} - ${event.time}
   المكان: ${event.venue}
   المدة: ${event.duration > 0 ? event.duration + ' ساعة' : '-'}
   الحضور: ${currentAttendees}/${event.capacity}
   الميزانية: ${event.budget > 0 ? event.budget.toLocaleString() + ' دج' : '-'}
   المنظم: ${event.organizerName}
   فئة الحضور: ${event.category || '-'}
   الحالة: ${event.status}
   يتطلب تأكيد الحضور: ${event.requiresRSVP ? 'نعم' : 'لا'}
   الوصف: ${event.description}
   البرنامج: ${event.program || '-'}
   المتطلبات: ${event.requirements || '-'}
   ملاحظات: ${event.notes || '-'}
`;
    });

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_الفعاليات_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم تصدير تقرير الفعاليات بنجاح', 'success');
}

// Print event details
function printEventDetails() {
    showAlert('سيتم تطوير ميزة الطباعة قريباً', 'info');
}
