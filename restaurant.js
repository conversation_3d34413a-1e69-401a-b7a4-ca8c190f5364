// Restaurant Management System

// Initialize restaurant data when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeRestaurant();
    loadEmployeeOptions();
    setupEventListeners();
    displayTodayMeals();
    updateRestaurantStats();
    setTodayDate();
});

// Initialize restaurant system
function initializeRestaurant() {
    // Initialize restaurant settings if not exists
    if (!localStorage.getItem('restaurantSettings')) {
        const defaultSettings = {
            defaultMealPrice: 300,
            defaultEmployeePayment: 100,
            lunchStartTime: '12:00',
            lunchEndTime: '14:00',
            dinnerStartTime: '19:00',
            dinnerEndTime: '21:00',
            subsidyPercentage: 67,
            maxSubsidyAmount: 200
        };
        localStorage.setItem('restaurantSettings', JSON.stringify(defaultSettings));
    }

    // Initialize meals data if not exists
    if (!localStorage.getItem('restaurantMeals')) {
        localStorage.setItem('restaurantMeals', JSON.stringify([]));
    }

    // Initialize subscriptions data if not exists
    if (!localStorage.getItem('restaurantSubscriptions')) {
        localStorage.setItem('restaurantSubscriptions', JSON.stringify([]));
    }

    // Initialize weekly menu if not exists (excluding Saturday and Friday - weekend)
    if (!localStorage.getItem('weeklyMenu')) {
        const defaultMenu = {
            sunday: '',
            monday: '',
            tuesday: '',
            wednesday: '',
            thursday: ''
        };
        localStorage.setItem('weeklyMenu', JSON.stringify(defaultMenu));
    }

    // Initialize cost calculator settings if not exists
    if (!localStorage.getItem('costCalculatorSettings')) {
        const defaultCostSettings = {
            beneficiaryCount: 0,
            mealCostPerPerson: 300,
            workingDaysPerWeek: 5
        };
        localStorage.setItem('costCalculatorSettings', JSON.stringify(defaultCostSettings));
    }
}

// Load employee options for dropdowns
function loadEmployeeOptions() {
    const employees = db.getEmployees();
    const mealEmployeeSelect = document.getElementById('mealEmployee');
    const subscriptionEmployeeSelect = document.getElementById('subscriptionEmployee');

    if (mealEmployeeSelect) {
        mealEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            mealEmployeeSelect.appendChild(option);
        });
    }

    if (subscriptionEmployeeSelect) {
        subscriptionEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            subscriptionEmployeeSelect.appendChild(option);
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Meal price calculation
    const mealPriceInput = document.getElementById('mealPrice');
    const employeePaymentInput = document.getElementById('employeePayment');
    const subsidyAmountInput = document.getElementById('subsidyAmount');

    if (mealPriceInput && employeePaymentInput && subsidyAmountInput) {
        mealPriceInput.addEventListener('input', calculateSubsidy);
        employeePaymentInput.addEventListener('input', calculateSubsidy);
    }

    // Subscription calculation
    const subscriptionDaysInput = document.getElementById('subscriptionDays');
    const dailyPaymentInput = document.getElementById('dailyEmployeePayment');
    const totalPaymentInput = document.getElementById('totalEmployeePayment');

    if (subscriptionDaysInput && dailyPaymentInput && totalPaymentInput) {
        subscriptionDaysInput.addEventListener('input', calculateTotalSubscription);
        dailyPaymentInput.addEventListener('input', calculateTotalSubscription);
    }

    // Set default date to today
    const mealDateInput = document.getElementById('mealDate');
    if (mealDateInput) {
        mealDateInput.value = new Date().toISOString().split('T')[0];
    }

    // Set default month to current month
    const subscriptionMonthInput = document.getElementById('subscriptionMonth');
    if (subscriptionMonthInput) {
        const now = new Date();
        const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
        subscriptionMonthInput.value = currentMonth;
    }

    // Load default settings
    loadRestaurantSettings();
}

// Calculate subsidy amount
function calculateSubsidy() {
    const mealPrice = parseFloat(document.getElementById('mealPrice').value) || 0;
    const employeePayment = parseFloat(document.getElementById('employeePayment').value) || 0;
    const subsidyAmount = Math.max(0, mealPrice - employeePayment);

    document.getElementById('subsidyAmount').value = subsidyAmount;
}

// Calculate total subscription amount
function calculateTotalSubscription() {
    const days = parseInt(document.getElementById('subscriptionDays').value) || 0;
    const dailyPayment = parseFloat(document.getElementById('dailyEmployeePayment').value) || 0;
    const totalPayment = days * dailyPayment;

    document.getElementById('totalEmployeePayment').value = totalPayment;
}

// Set today's date
function setTodayDate() {
    const today = new Date();
    const options = {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    };
    const todayDateElement = document.getElementById('todayDate');
    if (todayDateElement) {
        todayDateElement.textContent = today.toLocaleDateString('ar-DZ', options);
    }
}

// Save meal
function saveMeal() {
    const employeeId = document.getElementById('mealEmployee').value;
    const date = document.getElementById('mealDate').value;
    const type = document.getElementById('mealType').value;
    const price = parseFloat(document.getElementById('mealPrice').value);
    const employeePayment = parseFloat(document.getElementById('employeePayment').value);
    const subsidyAmount = parseFloat(document.getElementById('subsidyAmount').value);
    const description = document.getElementById('mealDescription').value.trim();

    // Validation
    if (!employeeId || !date || !type || !price || employeePayment === undefined) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (employeePayment > price) {
        showAlert('مساهمة الموظف لا يمكن أن تكون أكبر من سعر الوجبة', 'error');
        return;
    }

    // Get employee info
    const employee = db.getEmployees().find(emp => emp.id == employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Check for duplicate meal
    const meals = JSON.parse(localStorage.getItem('restaurantMeals') || '[]');
    const existingMeal = meals.find(meal =>
        meal.employeeId == employeeId &&
        meal.date === date &&
        meal.type === type
    );

    if (existingMeal) {
        showAlert(`الموظف ${employee.name} لديه وجبة ${type} مسجلة بالفعل في هذا التاريخ`, 'error');
        return;
    }

    // Create meal object
    const meal = {
        id: Date.now(),
        employeeId: parseInt(employeeId),
        employeeName: employee.name,
        employeeDepartment: employee.department,
        date,
        type,
        price,
        employeePayment,
        subsidyAmount,
        description,
        createdAt: new Date().toISOString()
    };

    // Save meal
    meals.push(meal);
    localStorage.setItem('restaurantMeals', JSON.stringify(meals));

    showAlert('تم حفظ الوجبة بنجاح', 'success');
    hideModal('addMealModal');
    resetMealForm();
    displayTodayMeals();
    updateRestaurantStats();
}

// Reset meal form
function resetMealForm() {
    document.getElementById('addMealForm').reset();
    document.getElementById('mealDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('subsidyAmount').value = '';
    loadRestaurantSettings();
}

// Display today's meals
function displayTodayMeals() {
    const today = new Date().toISOString().split('T')[0];
    const meals = JSON.parse(localStorage.getItem('restaurantMeals') || '[]');
    const todayMeals = meals.filter(meal => meal.date === today);

    const container = document.getElementById('todayMealsContainer');
    if (!container) return;

    if (todayMeals.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-utensils"></i>
                <h3>لا توجد وجبات مسجلة اليوم</h3>
                <p>اضغط على "إضافة وجبة جديدة" لبدء تسجيل الوجبات</p>
            </div>
        `;
        return;
    }

    container.innerHTML = todayMeals.map(meal => `
        <div class="meal-card">
            <div class="meal-header">
                <div class="meal-employee">
                    <i class="fas fa-user"></i>
                    <span class="employee-name">${meal.employeeName}</span>
                    <span class="employee-department">${meal.employeeDepartment}</span>
                </div>
                <div class="meal-type ${meal.type === 'غداء' ? 'lunch' : 'dinner'}">
                    <i class="fas fa-${meal.type === 'غداء' ? 'sun' : 'moon'}"></i>
                    ${meal.type}
                </div>
            </div>
            <div class="meal-details">
                <div class="meal-price">
                    <span class="label">سعر الوجبة:</span>
                    <span class="value">${meal.price.toLocaleString()} دج</span>
                </div>
                <div class="employee-payment">
                    <span class="label">مساهمة الموظف:</span>
                    <span class="value">${meal.employeePayment.toLocaleString()} دج</span>
                </div>
                <div class="subsidy-amount">
                    <span class="label">إعانة اللجنة:</span>
                    <span class="value">${meal.subsidyAmount.toLocaleString()} دج</span>
                </div>
            </div>
            ${meal.description ? `<div class="meal-description">${meal.description}</div>` : ''}
            <div class="meal-actions">
                <button class="btn btn-danger btn-sm" onclick="deleteMeal(${meal.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

// Delete meal
function deleteMeal(mealId) {
    if (!confirm('هل أنت متأكد من حذف هذه الوجبة؟')) {
        return;
    }

    const meals = JSON.parse(localStorage.getItem('restaurantMeals') || '[]');
    const updatedMeals = meals.filter(meal => meal.id !== mealId);
    localStorage.setItem('restaurantMeals', JSON.stringify(updatedMeals));

    showAlert('تم حذف الوجبة بنجاح', 'success');
    displayTodayMeals();
    updateRestaurantStats();
}

// Update restaurant statistics
function updateRestaurantStats() {
    const meals = JSON.parse(localStorage.getItem('restaurantMeals') || '[]');
    const today = new Date().toISOString().split('T')[0];

    // Today's statistics
    const todayMeals = meals.filter(meal => meal.date === today);
    const todayRevenue = todayMeals.reduce((sum, meal) => sum + meal.employeePayment, 0);
    const todaySubsidy = todayMeals.reduce((sum, meal) => sum + meal.subsidyAmount, 0);

    // Weekly statistics
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    const weekStartStr = weekStart.toISOString().split('T')[0];

    const weeklyMeals = meals.filter(meal => meal.date >= weekStartStr);

    // Update DOM elements
    const todayMealsCountEl = document.getElementById('todayMealsCount');
    const todayRevenueEl = document.getElementById('todayRevenue');
    const todaySubsidyEl = document.getElementById('todaySubsidy');
    const weeklyMealsCountEl = document.getElementById('weeklyMealsCount');

    if (todayMealsCountEl) todayMealsCountEl.textContent = todayMeals.length;
    if (todayRevenueEl) todayRevenueEl.textContent = `${todayRevenue.toLocaleString()} دج`;
    if (todaySubsidyEl) todaySubsidyEl.textContent = `${todaySubsidy.toLocaleString()} دج`;
    if (weeklyMealsCountEl) weeklyMealsCountEl.textContent = weeklyMeals.length;
}

// Save subscription
function saveSubscription() {
    const employeeId = document.getElementById('subscriptionEmployee').value;
    const month = document.getElementById('subscriptionMonth').value;
    const days = parseInt(document.getElementById('subscriptionDays').value);
    const dailyPayment = parseFloat(document.getElementById('dailyEmployeePayment').value);
    const totalPayment = parseFloat(document.getElementById('totalEmployeePayment').value);
    const status = document.getElementById('subscriptionStatus').value;

    // Validation
    if (!employeeId || !month || !days || dailyPayment === undefined) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Get employee info
    const employee = db.getEmployees().find(emp => emp.id == employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Check for existing subscription
    const subscriptions = JSON.parse(localStorage.getItem('restaurantSubscriptions') || '[]');
    const existingSubscription = subscriptions.find(sub =>
        sub.employeeId == employeeId && sub.month === month
    );

    if (existingSubscription) {
        showAlert(`الموظف ${employee.name} لديه اشتراك في هذا الشهر بالفعل`, 'error');
        return;
    }

    // Create subscription object
    const subscription = {
        id: Date.now(),
        employeeId: parseInt(employeeId),
        employeeName: employee.name,
        employeeDepartment: employee.department,
        month,
        days,
        dailyPayment,
        totalPayment,
        status,
        createdAt: new Date().toISOString()
    };

    // Save subscription
    subscriptions.push(subscription);
    localStorage.setItem('restaurantSubscriptions', JSON.stringify(subscriptions));

    showAlert('تم حفظ الاشتراك بنجاح', 'success');
    hideModal('addSubscriptionModal');
    resetSubscriptionForm();
}

// Reset subscription form
function resetSubscriptionForm() {
    document.getElementById('addSubscriptionForm').reset();
    const now = new Date();
    const currentMonth = now.getFullYear() + '-' + String(now.getMonth() + 1).padStart(2, '0');
    document.getElementById('subscriptionMonth').value = currentMonth;
    document.getElementById('totalEmployeePayment').value = '';
}

// Load restaurant settings
function loadRestaurantSettings() {
    const settings = JSON.parse(localStorage.getItem('restaurantSettings') || '{}');

    // Load default values in meal form
    const mealPriceInput = document.getElementById('mealPrice');
    const employeePaymentInput = document.getElementById('employeePayment');

    if (mealPriceInput && !mealPriceInput.value) {
        mealPriceInput.value = settings.defaultMealPrice || 300;
    }
    if (employeePaymentInput && !employeePaymentInput.value) {
        employeePaymentInput.value = settings.defaultEmployeePayment || 100;
    }

    // Load settings in settings modal
    const defaultMealPriceInput = document.getElementById('defaultMealPrice');
    const defaultEmployeePaymentInput = document.getElementById('defaultEmployeePayment');
    const lunchStartTimeInput = document.getElementById('lunchStartTime');
    const lunchEndTimeInput = document.getElementById('lunchEndTime');
    const dinnerStartTimeInput = document.getElementById('dinnerStartTime');
    const dinnerEndTimeInput = document.getElementById('dinnerEndTime');
    const subsidyPercentageInput = document.getElementById('subsidyPercentage');
    const maxSubsidyAmountInput = document.getElementById('maxSubsidyAmount');

    if (defaultMealPriceInput) defaultMealPriceInput.value = settings.defaultMealPrice || 300;
    if (defaultEmployeePaymentInput) defaultEmployeePaymentInput.value = settings.defaultEmployeePayment || 100;
    if (lunchStartTimeInput) lunchStartTimeInput.value = settings.lunchStartTime || '12:00';
    if (lunchEndTimeInput) lunchEndTimeInput.value = settings.lunchEndTime || '14:00';
    if (dinnerStartTimeInput) dinnerStartTimeInput.value = settings.dinnerStartTime || '19:00';
    if (dinnerEndTimeInput) dinnerEndTimeInput.value = settings.dinnerEndTime || '21:00';
    if (subsidyPercentageInput) subsidyPercentageInput.value = settings.subsidyPercentage || 67;
    if (maxSubsidyAmountInput) maxSubsidyAmountInput.value = settings.maxSubsidyAmount || 200;

    // Calculate subsidy if both price and payment are set
    if (mealPriceInput && employeePaymentInput && mealPriceInput.value && employeePaymentInput.value) {
        calculateSubsidy();
    }
}

// Save restaurant settings
function saveRestaurantSettings() {
    const settings = {
        defaultMealPrice: parseFloat(document.getElementById('defaultMealPrice').value) || 300,
        defaultEmployeePayment: parseFloat(document.getElementById('defaultEmployeePayment').value) || 100,
        lunchStartTime: document.getElementById('lunchStartTime').value || '12:00',
        lunchEndTime: document.getElementById('lunchEndTime').value || '14:00',
        dinnerStartTime: document.getElementById('dinnerStartTime').value || '19:00',
        dinnerEndTime: document.getElementById('dinnerEndTime').value || '21:00',
        subsidyPercentage: parseFloat(document.getElementById('subsidyPercentage').value) || 67,
        maxSubsidyAmount: parseFloat(document.getElementById('maxSubsidyAmount').value) || 200
    };

    localStorage.setItem('restaurantSettings', JSON.stringify(settings));
    showAlert('تم حفظ إعدادات المطعم بنجاح', 'success');
    hideModal('mealSettingsModal');
}

// Refresh today's meals
function refreshTodayMeals() {
    displayTodayMeals();
    updateRestaurantStats();
    showAlert('تم تحديث البيانات', 'success');
}

// Show today's meals section
function showTodayMeals() {
    document.getElementById('subscriptionsSection').style.display = 'none';
    document.querySelector('.today-meals-section').style.display = 'block';
    displayTodayMeals();
}

// Show employee subscriptions section
function showEmployeeSubscriptions() {
    document.querySelector('.today-meals-section').style.display = 'none';
    document.getElementById('subscriptionsSection').style.display = 'block';
    displaySubscriptions();
}

// Display subscriptions
function displaySubscriptions() {
    const subscriptions = JSON.parse(localStorage.getItem('restaurantSubscriptions') || '[]');
    const container = document.getElementById('subscriptionsContainer');

    if (!container) return;

    if (subscriptions.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-calendar-plus"></i>
                <h3>لا توجد اشتراكات مسجلة</h3>
                <p>اضغط على "إضافة اشتراك" لبدء تسجيل الاشتراكات الشهرية</p>
            </div>
        `;
        return;
    }

    // Sort subscriptions by month (newest first)
    subscriptions.sort((a, b) => new Date(b.month) - new Date(a.month));

    container.innerHTML = subscriptions.map(subscription => `
        <div class="subscription-card">
            <div class="subscription-header">
                <div class="subscription-employee">
                    <i class="fas fa-user"></i>
                    <span class="employee-name">${subscription.employeeName}</span>
                    <span class="employee-department">${subscription.employeeDepartment}</span>
                </div>
                <div class="subscription-status ${subscription.status}">
                    ${subscription.status}
                </div>
            </div>
            <div class="subscription-details">
                <div class="subscription-month">
                    <span class="label">الشهر:</span>
                    <span class="value">${formatMonth(subscription.month)}</span>
                </div>
                <div class="subscription-days">
                    <span class="label">عدد الأيام:</span>
                    <span class="value">${subscription.days} يوم</span>
                </div>
                <div class="daily-payment">
                    <span class="label">المساهمة اليومية:</span>
                    <span class="value">${subscription.dailyPayment.toLocaleString()} دج</span>
                </div>
                <div class="total-payment">
                    <span class="label">إجمالي المساهمة:</span>
                    <span class="value">${subscription.totalPayment.toLocaleString()} دج</span>
                </div>
            </div>
            <div class="subscription-actions">
                <button class="btn btn-danger btn-sm" onclick="deleteSubscription(${subscription.id})">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `).join('');
}

// Format month for display
function formatMonth(monthStr) {
    const [year, month] = monthStr.split('-');
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return `${monthNames[parseInt(month) - 1]} ${year}`;
}

// Delete subscription
function deleteSubscription(subscriptionId) {
    if (!confirm('هل أنت متأكد من حذف هذا الاشتراك؟')) {
        return;
    }

    const subscriptions = JSON.parse(localStorage.getItem('restaurantSubscriptions') || '[]');
    const updatedSubscriptions = subscriptions.filter(sub => sub.id !== subscriptionId);
    localStorage.setItem('restaurantSubscriptions', JSON.stringify(updatedSubscriptions));

    showAlert('تم حذف الاشتراك بنجاح', 'success');
    displaySubscriptions();
}

// Show meal history
function showMealHistory() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Show financial report
function showFinancialReport() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Generate daily report
function generateDailyReport() {
    const today = new Date().toISOString().split('T')[0];
    const meals = JSON.parse(localStorage.getItem('restaurantMeals') || '[]');
    const todayMeals = meals.filter(meal => meal.date === today);

    if (todayMeals.length === 0) {
        showAlert('لا توجد وجبات مسجلة اليوم لإنشاء التقرير', 'warning');
        return;
    }

    const totalRevenue = todayMeals.reduce((sum, meal) => sum + meal.employeePayment, 0);
    const totalSubsidy = todayMeals.reduce((sum, meal) => sum + meal.subsidyAmount, 0);
    const totalCost = todayMeals.reduce((sum, meal) => sum + meal.price, 0);

    const reportContent = `
تقرير مطعم الولاية اليومي
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

إحصائيات اليوم:
- عدد الوجبات: ${todayMeals.length}
- إجمالي التكلفة: ${totalCost.toLocaleString()} دج
- مساهمة الموظفين: ${totalRevenue.toLocaleString()} دج
- إعانة اللجنة: ${totalSubsidy.toLocaleString()} دج

تفاصيل الوجبات:
${todayMeals.map((meal, index) => `
${index + 1}. ${meal.employeeName} (${meal.employeeDepartment})
   نوع الوجبة: ${meal.type}
   سعر الوجبة: ${meal.price.toLocaleString()} دج
   مساهمة الموظف: ${meal.employeePayment.toLocaleString()} دج
   إعانة اللجنة: ${meal.subsidyAmount.toLocaleString()} دج
`).join('')}
    `;

    // Create and download the report
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_مطعم_الولاية_${today}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم إنشاء التقرير اليومي بنجاح', 'success');
}

// Generate subscription report
function generateSubscriptionReport() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Show cost calculator section
function showCostCalculator() {
    // Hide other sections
    document.querySelector('.today-meals-section').style.display = 'none';
    document.getElementById('subscriptionsSection').style.display = 'none';

    // Show cost calculator section
    document.getElementById('costCalculatorSection').style.display = 'block';

    // Load saved settings
    loadCostCalculatorSettings();
    loadWeeklyMenuDisplay();
}

// Load cost calculator settings
function loadCostCalculatorSettings() {
    const settings = JSON.parse(localStorage.getItem('costCalculatorSettings') || '{}');

    const beneficiaryCountInput = document.getElementById('beneficiaryCount');
    const mealCostInput = document.getElementById('mealCostPerPerson');
    const workingDaysInput = document.getElementById('workingDaysPerWeek');

    if (beneficiaryCountInput && settings.beneficiaryCount) {
        beneficiaryCountInput.value = settings.beneficiaryCount;
    }
    if (mealCostInput && settings.mealCostPerPerson) {
        mealCostInput.value = settings.mealCostPerPerson;
    }
    if (workingDaysInput && settings.workingDaysPerWeek) {
        workingDaysInput.value = settings.workingDaysPerWeek;
    }
}

// Calculate costs
function calculateCosts() {
    const beneficiaryCount = parseInt(document.getElementById('beneficiaryCount').value);
    const mealCostPerPerson = parseFloat(document.getElementById('mealCostPerPerson').value);
    const workingDaysPerWeek = parseInt(document.getElementById('workingDaysPerWeek').value);

    // Validation
    if (!beneficiaryCount || beneficiaryCount <= 0) {
        showAlert('يرجى إدخال عدد الموظفين المستفيدين', 'error');
        return;
    }

    if (!mealCostPerPerson || mealCostPerPerson <= 0) {
        showAlert('يرجى إدخال تكلفة الوجبة للشخص الواحد', 'error');
        return;
    }

    // Calculate costs
    const weeklyCost = beneficiaryCount * mealCostPerPerson * workingDaysPerWeek;
    const monthlyCost = weeklyCost * 4.33; // Average weeks per month
    const yearlyCost = weeklyCost * 52; // 52 weeks per year

    // Update display
    document.getElementById('weeklyCost').textContent = `${weeklyCost.toLocaleString()} دج`;
    document.getElementById('monthlyCost').textContent = `${Math.round(monthlyCost).toLocaleString()} دج`;
    document.getElementById('yearlyCost').textContent = `${yearlyCost.toLocaleString()} دج`;

    // Update details
    document.getElementById('weeklyDetails').textContent =
        `${beneficiaryCount} موظف × ${workingDaysPerWeek} أيام × ${mealCostPerPerson.toLocaleString()} دج`;
    document.getElementById('monthlyDetails').textContent =
        `4.33 أسبوع × ${weeklyCost.toLocaleString()} دج`;
    document.getElementById('yearlyDetails').textContent =
        `52 أسبوع × ${weeklyCost.toLocaleString()} دج`;

    // Show results
    document.getElementById('costResults').style.display = 'block';

    // Save settings
    const settings = {
        beneficiaryCount,
        mealCostPerPerson,
        workingDaysPerWeek
    };
    localStorage.setItem('costCalculatorSettings', JSON.stringify(settings));

    showAlert('تم حساب التكاليف بنجاح', 'success');
}

// Save weekly menu (working days only - excluding Saturday and Friday)
function saveWeeklyMenu() {
    const menu = {
        sunday: document.getElementById('sundayMenu').value.trim(),
        monday: document.getElementById('mondayMenu').value.trim(),
        tuesday: document.getElementById('tuesdayMenu').value.trim(),
        wednesday: document.getElementById('wednesdayMenu').value.trim(),
        thursday: document.getElementById('thursdayMenu').value.trim()
    };

    localStorage.setItem('weeklyMenu', JSON.stringify(menu));
    showAlert('تم حفظ قائمة الطعام الأسبوعية بنجاح', 'success');
    hideModal('weeklyMenuModal');
    loadWeeklyMenuDisplay();
}

// Load weekly menu in modal (working days only)
function loadWeeklyMenu() {
    const menu = JSON.parse(localStorage.getItem('weeklyMenu') || '{}');

    document.getElementById('sundayMenu').value = menu.sunday || '';
    document.getElementById('mondayMenu').value = menu.monday || '';
    document.getElementById('tuesdayMenu').value = menu.tuesday || '';
    document.getElementById('wednesdayMenu').value = menu.wednesday || '';
    document.getElementById('thursdayMenu').value = menu.thursday || '';
}

// Load weekly menu display
function loadWeeklyMenuDisplay() {
    const menu = JSON.parse(localStorage.getItem('weeklyMenu') || '{}');
    const menuDisplay = document.getElementById('weeklyMenuDisplay');
    const menuGrid = document.getElementById('menuWeekGrid');

    if (!menuGrid) return;

    // Check if any menu items exist
    const hasMenuItems = Object.values(menu).some(item => item && item.trim() !== '');

    if (!hasMenuItems) {
        menuDisplay.style.display = 'none';
        return;
    }

    menuDisplay.style.display = 'block';

    const days = [
        { key: 'sunday', name: 'الأحد' },
        { key: 'monday', name: 'الاثنين' },
        { key: 'tuesday', name: 'الثلاثاء' },
        { key: 'wednesday', name: 'الأربعاء' },
        { key: 'thursday', name: 'الخميس' }
    ];

    menuGrid.innerHTML = days.map(day => {
        const menuItems = menu[day.key] || '';
        if (!menuItems.trim()) return '';

        return `
            <div class="menu-day-display">
                <div class="day-name">
                    <i class="fas fa-calendar-day"></i>
                    ${day.name}
                </div>
                <div class="menu-content">
                    ${menuItems}
                </div>
            </div>
        `;
    }).filter(item => item !== '').join('');
}

// Export cost report
function exportCostReport() {
    const settings = JSON.parse(localStorage.getItem('costCalculatorSettings') || '{}');
    const menu = JSON.parse(localStorage.getItem('weeklyMenu') || '{}');

    if (!settings.beneficiaryCount || !settings.mealCostPerPerson) {
        showAlert('يرجى حساب التكاليف أولاً', 'warning');
        return;
    }

    const weeklyCost = settings.beneficiaryCount * settings.mealCostPerPerson * settings.workingDaysPerWeek;
    const monthlyCost = weeklyCost * 4.33;
    const yearlyCost = weeklyCost * 52;

    const reportContent = `
تقرير تكلفة الإطعام - مطعم الولاية
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

معطيات الحساب:
- عدد الموظفين المستفيدين: ${settings.beneficiaryCount} موظف
- تكلفة الوجبة للشخص الواحد: ${settings.mealCostPerPerson.toLocaleString()} دج
- أيام العمل في الأسبوع: ${settings.workingDaysPerWeek} أيام

التكاليف المحسوبة:
- التكلفة الأسبوعية: ${weeklyCost.toLocaleString()} دج
- التكلفة الشهرية: ${Math.round(monthlyCost).toLocaleString()} دج
- التكلفة السنوية: ${yearlyCost.toLocaleString()} دج

قائمة الطعام الأسبوعية (أيام العمل):
${Object.entries(menu).map(([day, items]) => {
    const dayNames = {
        sunday: 'الأحد',
        monday: 'الاثنين',
        tuesday: 'الثلاثاء',
        wednesday: 'الأربعاء',
        thursday: 'الخميس'
    };
    return items ? `${dayNames[day]}: ${items}` : '';
}).filter(item => item !== '').join('\n')}

أيام العطلة الأسبوعية: السبت والجمعة

ملاحظات:
- التكلفة الشهرية محسوبة على أساس 4.33 أسبوع في الشهر
- التكلفة السنوية محسوبة على أساس 52 أسبوع في السنة
- هذه التكاليف تشمل وجبة الغداء فقط
    `;

    // Create and download the report
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_تكلفة_الإطعام_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم تصدير تقرير التكلفة بنجاح', 'success');
}

// Override showModal to load weekly menu when opening the modal
const originalShowModal = window.showModal;
window.showModal = function(modalId) {
    if (modalId === 'weeklyMenuModal') {
        loadWeeklyMenu();
    }
    originalShowModal(modalId);
};
