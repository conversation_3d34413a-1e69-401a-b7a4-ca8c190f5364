// Database Adapter - Bridge between old and new database systems
class DatabaseAdapter {
    constructor() {
        this.isAdvancedDBReady = false;
        this.fallbackToLocalStorage = false;
        this.initializeAdapter();
    }

    async initializeAdapter() {
        try {
            // Wait for advanced database to be ready
            if (typeof advancedDB !== 'undefined') {
                await advancedDB.init();
                this.isAdvancedDBReady = true;
                console.log('Using advanced IndexedDB database');
                
                // Migrate existing localStorage data if needed
                await this.migrateFromLocalStorage();
            } else {
                this.fallbackToLocalStorage = true;
                console.log('Falling back to localStorage database');
            }
        } catch (error) {
            console.error('Failed to initialize advanced database, falling back to localStorage:', error);
            this.fallbackToLocalStorage = true;
        }
    }

    // Migrate data from localStorage to IndexedDB
    async migrateFromLocalStorage() {
        try {
            // Check if we have data in localStorage but not in IndexedDB
            const existingEmployees = await this.getEmployees();
            if (existingEmployees.length > 0) {
                return; // Data already exists in IndexedDB
            }

            // Get data from localStorage (old system)
            const localStorageEmployees = JSON.parse(localStorage.getItem('employees') || '[]');
            
            if (localStorageEmployees.length > 0) {
                console.log('Migrating employees from localStorage to IndexedDB...');
                
                for (const emp of localStorageEmployees) {
                    try {
                        // Convert old format to new format
                        const newEmployee = {
                            name: emp.name,
                            employeeId: emp.employeeId || `EMP${String(emp.id).padStart(3, '0')}`,
                            department: emp.department,
                            position: emp.position,
                            phone: emp.phone,
                            email: emp.email,
                            joinDate: emp.joinDate || emp.hireDate,
                            salary: emp.salary,
                            status: emp.status || 'نشط',
                            address: emp.address || '',
                            nationalId: emp.nationalId || '',
                            birthDate: emp.birthDate || '',
                            maritalStatus: emp.maritalStatus || '',
                            emergencyContact: emp.emergencyContact || '',
                            createdAt: emp.createdAt || new Date().toISOString()
                        };
                        
                        await advancedDB.addEmployee(newEmployee);
                    } catch (error) {
                        console.error('Error migrating employee:', emp.name, error);
                    }
                }
                
                console.log('Migration completed successfully');
            }
        } catch (error) {
            console.error('Error during migration:', error);
        }
    }

    // Employee methods with fallback
    async getEmployees() {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.getEmployees();
        } else {
            // Fallback to localStorage
            return JSON.parse(localStorage.getItem('employees') || '[]');
        }
    }

    async getEmployeeById(id) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.getEmployeeById(id);
        } else {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            return employees.find(emp => emp.id === id);
        }
    }

    async addEmployee(employeeData) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.addEmployee(employeeData);
        } else {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            employeeData.id = Date.now();
            employeeData.createdAt = new Date().toISOString();
            employees.push(employeeData);
            localStorage.setItem('employees', JSON.stringify(employees));
            return employeeData;
        }
    }

    async updateEmployee(id, employeeData) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.updateEmployee(id, employeeData);
        } else {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const index = employees.findIndex(emp => emp.id === id);
            if (index !== -1) {
                employees[index] = { ...employees[index], ...employeeData, updatedAt: new Date().toISOString() };
                localStorage.setItem('employees', JSON.stringify(employees));
                return employees[index];
            }
            return null;
        }
    }

    async deleteEmployee(id) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            // Use advanced database with loan checking
            return await advancedDB.deleteEmployee(id);
        } else {
            // Check for active loans in localStorage
            const hasActiveLoans = await this.employeeHasActiveLoans(id);
            
            if (hasActiveLoans) {
                throw new Error('لا يمكن حذف الموظف لأنه لديه قروض جارية التسديد. يجب تسديد جميع القروض أولاً.');
            }

            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const filteredEmployees = employees.filter(emp => emp.id !== id);
            localStorage.setItem('employees', JSON.stringify(filteredEmployees));
            return true;
        }
    }

    // Check if employee has active loans (works with both systems)
    async employeeHasActiveLoans(employeeId) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.employeeHasActiveLoans(employeeId);
        } else {
            // Check in localStorage (current financial year)
            const currentYear = localStorage.getItem('currentFinancialYear') || '2025';
            const loans = JSON.parse(localStorage.getItem(`loans_${currentYear}`) || '[]');
            const payments = JSON.parse(localStorage.getItem(`loanPayments_${currentYear}`) || '[]');
            
            return loans.some(loan => {
                if (loan.employeeId !== parseInt(employeeId)) return false;
                if (loan.status === 'ملغي') return false;
                
                // Calculate remaining amount
                const loanPayments = payments.filter(payment => payment.loanId === loan.id);
                const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
                const remainingAmount = loan.amount - paidAmount;
                
                return remainingAmount > 0;
            });
        }
    }

    // Search employees
    async searchEmployees(searchTerm) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.searchEmployees(searchTerm);
        } else {
            const employees = JSON.parse(localStorage.getItem('employees') || '[]');
            const term = searchTerm.toLowerCase();
            
            return employees.filter(emp => 
                emp.name.toLowerCase().includes(term) ||
                (emp.employeeId && emp.employeeId.toLowerCase().includes(term)) ||
                emp.department.toLowerCase().includes(term) ||
                emp.position.toLowerCase().includes(term)
            );
        }
    }

    // Department methods
    async getDepartments() {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.getDepartments();
        } else {
            // Return default departments for localStorage
            return [
                { id: 1, name: 'الإدارة العامة', description: 'الإدارة العليا والتخطيط الاستراتيجي' },
                { id: 2, name: 'الموارد البشرية', description: 'إدارة شؤون الموظفين والتوظيف' },
                { id: 3, name: 'المالية', description: 'الشؤون المالية والمحاسبة' },
                { id: 4, name: 'التسويق', description: 'التسويق والمبيعات' },
                { id: 5, name: 'تقنية المعلومات', description: 'تطوير وصيانة الأنظمة' },
                { id: 6, name: 'العمليات', description: 'العمليات التشغيلية' },
                { id: 7, name: 'خدمة العملاء', description: 'خدمة ودعم العملاء' }
            ];
        }
    }

    // Database management
    async exportDatabase() {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.exportDatabase();
        } else {
            // Export localStorage data
            const data = {
                employees: JSON.parse(localStorage.getItem('employees') || '[]'),
                exportDate: new Date().toISOString(),
                source: 'localStorage'
            };
            
            return JSON.stringify(data, null, 2);
        }
    }

    async importDatabase(jsonData) {
        if (this.isAdvancedDBReady && !this.fallbackToLocalStorage) {
            return await advancedDB.importDatabase(jsonData);
        } else {
            try {
                const data = JSON.parse(jsonData);
                
                if (data.employees) {
                    localStorage.setItem('employees', JSON.stringify(data.employees));
                }
                
                return true;
            } catch (error) {
                throw new Error('فشل في استيراد قاعدة البيانات: ' + error.message);
            }
        }
    }

    // Get database info
    getDatabaseInfo() {
        return {
            type: this.isAdvancedDBReady && !this.fallbackToLocalStorage ? 'IndexedDB' : 'localStorage',
            isAdvanced: this.isAdvancedDBReady && !this.fallbackToLocalStorage,
            canExport: true,
            canImport: true,
            supportsSearch: true,
            supportsLoanProtection: true
        };
    }
}

// Create global database adapter instance
const db = new DatabaseAdapter();

// Initialize adapter when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await db.initializeAdapter();
        console.log('Database adapter initialized:', db.getDatabaseInfo());
    } catch (error) {
        console.error('Failed to initialize database adapter:', error);
    }
});
