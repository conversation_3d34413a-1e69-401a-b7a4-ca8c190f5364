<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الرحلات والاصطياف - نظام إدارة لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-hands-helping"></i>
                    <h1>نظام إدارة لجنة الخدمات الاجتماعية</h1>
                </div>
                <div class="user-info">
                    <span class="date" id="currentDate"></span>
                    <div class="user-profile">
                        <i class="fas fa-user-circle"></i>
                        <span>مدير النظام</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="employees.html"><i class="fas fa-users"></i> الموظفين</a></li>
                <li><a href="financial.html"><i class="fas fa-money-bill-wave"></i> الخدمات المالية</a></li>
                <li><a href="loans.html"><i class="fas fa-hand-holding-usd"></i> القروض والسلفيات</a></li>
                <li><a href="trips.html" class="active"><i class="fas fa-plane"></i> الرحلات</a></li>
                <li><a href="events.html"><i class="fas fa-calendar-alt"></i> الفعاليات</a></li>
                <li><a href="health.html"><i class="fas fa-heartbeat"></i> الإعانات الصحية</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h2><i class="fas fa-plane"></i> الرحلات والاصطياف</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showModal('addTripModal')">
                        <i class="fas fa-plus"></i> تنظيم رحلة جديدة
                    </button>
                    <button class="btn btn-success" onclick="exportTripsReport()">
                        <i class="fas fa-file-excel"></i> تصدير التقرير
                    </button>
                </div>
            </div>

            <!-- Trips Statistics -->
            <section class="trips-stats">
                <div class="stats-grid">
                    <div class="stat-card total-trips">
                        <div class="stat-icon" style="background: #3498db;">
                            <i class="fas fa-map-marked-alt"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalTrips">0</h3>
                            <p>إجمالي الرحلات</p>
                        </div>
                    </div>
                    <div class="stat-card active-trips">
                        <div class="stat-icon" style="background: #27ae60;">
                            <i class="fas fa-plane-departure"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="activeTrips">0</h3>
                            <p>الرحلات النشطة</p>
                        </div>
                    </div>
                    <div class="stat-card total-participants">
                        <div class="stat-icon" style="background: #f39c12;">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalParticipants">0</h3>
                            <p>إجمالي المشاركين</p>
                        </div>
                    </div>
                    <div class="stat-card total-revenue">
                        <div class="stat-icon" style="background: #e74c3c;">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalRevenue">0 دج</h3>
                            <p>إجمالي الإيرادات</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="actions-grid">
                    <div class="action-card" onclick="showActiveTrips()">
                        <i class="fas fa-plane-departure"></i>
                        <h4>الرحلات النشطة</h4>
                        <p>عرض الرحلات المتاحة للتسجيل</p>
                    </div>
                    <div class="action-card" onclick="showCompletedTrips()">
                        <i class="fas fa-plane-arrival"></i>
                        <h4>الرحلات المكتملة</h4>
                        <p>عرض الرحلات المنتهية</p>
                    </div>
                    <div class="action-card" onclick="showParticipantsList()">
                        <i class="fas fa-list-ul"></i>
                        <h4>قائمة المشاركين</h4>
                        <p>إدارة تسجيلات المشاركين</p>
                    </div>
                    <div class="action-card" onclick="showDestinations()">
                        <i class="fas fa-globe-africa"></i>
                        <h4>الوجهات السياحية</h4>
                        <p>إدارة الوجهات والبرامج</p>
                    </div>
                </div>
            </section>

            <!-- Trips Management -->
            <section class="trips-management">
                <div class="section-header">
                    <h3><i class="fas fa-list"></i> إدارة الرحلات</h3>
                    <div class="section-actions">
                        <div class="search-input">
                            <input type="text" id="tripsSearch" placeholder="البحث في الرحلات..." onkeyup="searchTrips()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="tripsFilter" onchange="filterTrips()">
                            <option value="">جميع الرحلات</option>
                            <option value="نشطة">الرحلات النشطة</option>
                            <option value="مكتملة">الرحلات المكتملة</option>
                            <option value="ملغية">الرحلات الملغية</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>اسم الرحلة</th>
                                <th>الوجهة</th>
                                <th>تاريخ المغادرة</th>
                                <th>تاريخ العودة</th>
                                <th>السعر</th>
                                <th>المشاركين</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="tripsTableBody">
                            <!-- Trips data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </main>

    <!-- Add Trip Modal -->
    <div id="addTripModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> تنظيم رحلة جديدة</h3>
                <span class="close" onclick="hideModal('addTripModal')">&times;</span>
            </div>
            <form id="addTripForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="tripName">اسم الرحلة *</label>
                        <input type="text" id="tripName" name="tripName" required placeholder="مثال: رحلة تونس الصيفية">
                    </div>
                    <div class="form-group">
                        <label for="tripDestination">الوجهة *</label>
                        <select id="tripDestination" name="destination" required>
                            <option value="">اختر الوجهة</option>
                            <option value="تونس">تونس</option>
                            <option value="تركيا">تركيا</option>
                            <option value="المغرب">المغرب</option>
                            <option value="مصر">مصر</option>
                            <option value="الأردن">الأردن</option>
                            <option value="الإمارات">الإمارات</option>
                            <option value="قسنطينة">قسنطينة</option>
                            <option value="وهران">وهران</option>
                            <option value="عنابة">عنابة</option>
                            <option value="تلمسان">تلمسان</option>
                            <option value="بجاية">بجاية</option>
                            <option value="جيجل">جيجل</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tripType">نوع الرحلة *</label>
                        <select id="tripType" name="tripType" required>
                            <option value="">اختر نوع الرحلة</option>
                            <option value="داخلية">رحلة داخلية</option>
                            <option value="خارجية">رحلة خارجية</option>
                            <option value="اصطياف">اصطياف صيفي</option>
                            <option value="دينية">رحلة دينية</option>
                            <option value="ثقافية">رحلة ثقافية</option>
                            <option value="ترفيهية">رحلة ترفيهية</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tripDepartureDate">تاريخ المغادرة *</label>
                        <input type="date" id="tripDepartureDate" name="departureDate" required>
                    </div>
                    <div class="form-group">
                        <label for="tripReturnDate">تاريخ العودة *</label>
                        <input type="date" id="tripReturnDate" name="returnDate" required>
                    </div>
                    <div class="form-group">
                        <label for="tripDuration">مدة الرحلة (أيام)</label>
                        <input type="number" id="tripDuration" name="duration" min="1" max="30" readonly>
                    </div>
                    <div class="form-group">
                        <label for="tripPrice">سعر الرحلة (دج) *</label>
                        <input type="number" id="tripPrice" name="price" required min="0" step="1000">
                    </div>
                    <div class="form-group">
                        <label for="tripMaxParticipants">الحد الأقصى للمشاركين *</label>
                        <input type="number" id="tripMaxParticipants" name="maxParticipants" required min="1" max="200">
                    </div>
                    <div class="form-group">
                        <label for="tripHotel">الفندق/الإقامة</label>
                        <input type="text" id="tripHotel" name="hotel" placeholder="اسم الفندق أو مكان الإقامة">
                    </div>
                    <div class="form-group">
                        <label for="tripTransport">وسيلة النقل</label>
                        <select id="tripTransport" name="transport">
                            <option value="">اختر وسيلة النقل</option>
                            <option value="حافلة">حافلة</option>
                            <option value="طائرة">طائرة</option>
                            <option value="قطار">قطار</option>
                            <option value="سيارة خاصة">سيارة خاصة</option>
                            <option value="مختلطة">وسائل مختلطة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tripMeals">الوجبات المشمولة</label>
                        <select id="tripMeals" name="meals">
                            <option value="">اختر نوع الوجبات</option>
                            <option value="إفطار فقط">إفطار فقط</option>
                            <option value="نصف إقامة">نصف إقامة (إفطار + عشاء)</option>
                            <option value="إقامة كاملة">إقامة كاملة (3 وجبات)</option>
                            <option value="بدون وجبات">بدون وجبات</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="tripRegistrationDeadline">آخر موعد للتسجيل *</label>
                        <input type="date" id="tripRegistrationDeadline" name="registrationDeadline" required>
                    </div>
                    <div class="form-group full-width">
                        <label for="tripDescription">وصف الرحلة *</label>
                        <textarea id="tripDescription" name="description" rows="3" required placeholder="وصف مفصل للرحلة والأنشطة المتضمنة..."></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label for="tripProgram">برنامج الرحلة</label>
                        <textarea id="tripProgram" name="program" rows="4" placeholder="البرنامج اليومي للرحلة والأماكن المزارة..."></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label for="tripNotes">ملاحظات إضافية</label>
                        <textarea id="tripNotes" name="notes" rows="2" placeholder="أي ملاحظات أو شروط خاصة..."></textarea>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addTripModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveTrip()">حفظ الرحلة</button>
            </div>
        </div>
    </div>

    <!-- Trip Details Modal -->
    <div id="tripDetailsModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3><i class="fas fa-info-circle"></i> تفاصيل الرحلة</h3>
                <span class="close" onclick="hideModal('tripDetailsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Trip Information -->
                <div class="trip-details-section">
                    <h4><i class="fas fa-map-marked-alt"></i> معلومات الرحلة</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>اسم الرحلة:</label>
                            <span id="detailsTripName">-</span>
                        </div>
                        <div class="detail-item">
                            <label>الوجهة:</label>
                            <span id="detailsTripDestination">-</span>
                        </div>
                        <div class="detail-item">
                            <label>نوع الرحلة:</label>
                            <span id="detailsTripType">-</span>
                        </div>
                        <div class="detail-item">
                            <label>تاريخ المغادرة:</label>
                            <span id="detailsDepartureDate">-</span>
                        </div>
                        <div class="detail-item">
                            <label>تاريخ العودة:</label>
                            <span id="detailsReturnDate">-</span>
                        </div>
                        <div class="detail-item">
                            <label>مدة الرحلة:</label>
                            <span id="detailsDuration">-</span>
                        </div>
                        <div class="detail-item">
                            <label>سعر الرحلة:</label>
                            <span id="detailsPrice">-</span>
                        </div>
                        <div class="detail-item">
                            <label>الحد الأقصى للمشاركين:</label>
                            <span id="detailsMaxParticipants">-</span>
                        </div>
                        <div class="detail-item">
                            <label>المشاركين الحاليين:</label>
                            <span id="detailsCurrentParticipants">-</span>
                        </div>
                        <div class="detail-item">
                            <label>الفندق/الإقامة:</label>
                            <span id="detailsHotel">-</span>
                        </div>
                        <div class="detail-item">
                            <label>وسيلة النقل:</label>
                            <span id="detailsTransport">-</span>
                        </div>
                        <div class="detail-item">
                            <label>الوجبات:</label>
                            <span id="detailsMeals">-</span>
                        </div>
                        <div class="detail-item">
                            <label>آخر موعد للتسجيل:</label>
                            <span id="detailsRegistrationDeadline">-</span>
                        </div>
                        <div class="detail-item">
                            <label>الحالة:</label>
                            <span id="detailsStatus">-</span>
                        </div>
                        <div class="detail-item full-width">
                            <label>وصف الرحلة:</label>
                            <span id="detailsDescription">-</span>
                        </div>
                        <div class="detail-item full-width">
                            <label>برنامج الرحلة:</label>
                            <span id="detailsProgram">-</span>
                        </div>
                        <div class="detail-item full-width">
                            <label>ملاحظات:</label>
                            <span id="detailsNotes">-</span>
                        </div>
                    </div>
                </div>

                <!-- Participants List -->
                <div class="participants-section">
                    <h4><i class="fas fa-users"></i> قائمة المشاركين</h4>
                    <div class="participants-list" id="participantsList">
                        <!-- Participants will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('tripDetailsModal')">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printTripDetails()">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
    </div>

    <!-- Register for Trip Modal -->
    <div id="registerTripModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> تسجيل في الرحلة</h3>
                <span class="close" onclick="hideModal('registerTripModal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="registerTripId">

                <!-- Trip Info -->
                <div class="trip-info-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات الرحلة</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>اسم الرحلة:</label>
                            <span id="registerTripName">-</span>
                        </div>
                        <div class="info-item">
                            <label>الوجهة:</label>
                            <span id="registerTripDestination">-</span>
                        </div>
                        <div class="info-item">
                            <label>السعر:</label>
                            <span id="registerTripPrice">-</span>
                        </div>
                        <div class="info-item">
                            <label>المقاعد المتاحة:</label>
                            <span id="registerAvailableSeats">-</span>
                        </div>
                    </div>
                </div>

                <!-- Registration Form -->
                <form id="registerTripForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="registerEmployee">الموظف *</label>
                            <select id="registerEmployee" name="employee" required>
                                <option value="">اختر الموظف</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="registerCompanions">عدد المرافقين</label>
                            <input type="number" id="registerCompanions" name="companions" min="0" max="5" value="0">
                        </div>
                        <div class="form-group">
                            <label for="registerPaymentMethod">طريقة الدفع *</label>
                            <select id="registerPaymentMethod" name="paymentMethod" required>
                                <option value="">اختر طريقة الدفع</option>
                                <option value="نقدي">نقدي</option>
                                <option value="تحويل بنكي">تحويل بنكي</option>
                                <option value="خصم من الراتب">خصم من الراتب</option>
                                <option value="أقساط">دفع على أقساط</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="registerPhone">رقم الهاتف *</label>
                            <input type="tel" id="registerPhone" name="phone" required placeholder="0555123456">
                        </div>
                        <div class="form-group full-width">
                            <label for="registerNotes">ملاحظات التسجيل</label>
                            <textarea id="registerNotes" name="notes" rows="3" placeholder="أي ملاحظات أو طلبات خاصة..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('registerTripModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveTripRegistration()">
                    <i class="fas fa-check"></i> تأكيد التسجيل
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 نظام إدارة لجنة الخدمات الاجتماعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="database.js"></script>
    <script src="script.js"></script>
    <script src="trips.js"></script>
</body>
</html>
