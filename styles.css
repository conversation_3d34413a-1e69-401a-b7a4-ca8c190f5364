/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background-color: #f5f7fa;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #ffd700;
}

.logo h1 {
    font-size: 1.8rem;
    font-weight: 700;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.date {
    background: rgba(255,255,255,0.2);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 500;
}

.user-profile {
    display: flex;
    align-items: center;
    gap: 8px;
}

.user-profile i {
    font-size: 1.5rem;
}

/* Navigation Styles */
.navbar {
    background: #2c3e50;
    padding: 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.nav-menu {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
}

.nav-menu li {
    flex: 1;
}

.nav-menu a {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 15px 10px;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.nav-menu a:hover,
.nav-menu a.active {
    background: #34495e;
    border-bottom-color: #3498db;
}

.nav-menu i {
    font-size: 1.1rem;
}

/* Main Content */
.main-content {
    padding: 30px 0;
    min-height: calc(100vh - 200px);
}

/* Dashboard Stats */
.dashboard-stats {
    margin-bottom: 40px;
}

.dashboard-stats h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 2rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-icon { background: #3498db; }
.stat-card:nth-child(2) .stat-icon { background: #2ecc71; }
.stat-card:nth-child(3) .stat-icon { background: #e74c3c; }
.stat-card:nth-child(4) .stat-icon { background: #f39c12; }

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-info p {
    color: #7f8c8d;
    font-weight: 500;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 40px;
}

.quick-actions h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8rem;
    text-align: center;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.action-card {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: #3498db;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.action-card i {
    font-size: 3rem;
    color: #3498db;
    margin-bottom: 15px;
}

.action-card h3 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.3rem;
}

.action-card p {
    color: #7f8c8d;
    line-height: 1.5;
}

/* Recent Activities */
.recent-activities {
    margin-bottom: 40px;
}

.recent-activities h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8rem;
    text-align: center;
}

.activities-list {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.activity-item {
    padding: 20px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    align-items: center;
    gap: 15px;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.activity-info {
    flex: 1;
}

.activity-info h4 {
    color: #2c3e50;
    margin-bottom: 5px;
}

.activity-info p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

.activity-time {
    color: #95a5a6;
    font-size: 0.8rem;
}

/* Financial Summary */
.financial-summary h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 1.8rem;
    text-align: center;
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 25px;
}

.summary-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    text-align: center;
}

.summary-card h3 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.amount {
    font-size: 1.8rem;
    font-weight: 700;
}

.amount.positive {
    color: #2ecc71;
}

.amount.negative {
    color: #e74c3c;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    text-align: center;
    padding: 20px 0;
    margin-top: 50px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 15px;
    }

    .nav-menu {
        flex-direction: column;
    }

    .nav-menu a {
        justify-content: flex-start;
        padding: 12px 20px;
    }

    .stats-grid,
    .actions-grid,
    .summary-grid {
        grid-template-columns: 1fr;
    }

    .logo h1 {
        font-size: 1.4rem;
    }
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-20 { margin-bottom: 20px; }
.mt-20 { margin-top: 20px; }
.p-20 { padding: 20px; }

/* Page Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.page-header h2 {
    color: #2c3e50;
    font-size: 1.8rem;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.btn-warning {
    background: #f39c12;
    color: white;
}

.btn-warning:hover {
    background: #e67e22;
}

.btn-danger {
    background: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background: #c0392b;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 0.8rem;
}

.btn-icon {
    width: 35px;
    height: 35px;
    padding: 0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Search and Filter */
.search-filter {
    margin-bottom: 30px;
}

.search-bar {
    background: white;
    padding: 20px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-input i {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
}

.search-input input {
    width: 100%;
    padding: 12px 45px 12px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.search-input input:focus {
    outline: none;
    border-color: #3498db;
}

.filter-controls {
    display: flex;
    gap: 15px;
    align-items: center;
}

.filter-controls select {
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    background: white;
    cursor: pointer;
}

/* Data Table */
.table-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background: #34495e;
    color: white;
    padding: 15px 10px;
    text-align: right;
    font-weight: 600;
    border-bottom: 2px solid #2c3e50;
}

.data-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #ecf0f1;
    vertical-align: middle;
}

.data-table tr:hover {
    background: #f8f9fa;
}

/* Employee Photo */
.employee-photo {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto;
}

.employee-photo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.photo-placeholder {
    width: 100%;
    height: 100%;
    background: #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #7f8c8d;
    font-size: 1.2rem;
}

.employee-photo-large {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 20px;
}

.employee-photo-large img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Badges */
.gender-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.gender-badge.male {
    background: #e3f2fd;
    color: #1976d2;
}

.gender-badge.female {
    background: #fce4ec;
    color: #c2185b;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.active {
    background: #e8f5e8;
    color: #2e7d32;
}

.status-badge.inactive {
    background: #ffebee;
    color: #d32f2f;
}

.children-count {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    color: #f39c12;
    font-weight: 500;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 15px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 15px 15px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #e74c3c;
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid #ecf0f1;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 0 0 15px 15px;
}

/* Form Styles */
.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    grid-column: 1 / -1;
}

.form-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #3498db;
}

/* Children Section */
.children-section {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 2px solid #ecf0f1;
}

.children-section h4 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.child-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 15px;
    border: 1px solid #ecf0f1;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #7f8c8d;
}

.empty-state i {
    color: #bdc3c7;
    margin-bottom: 15px;
}

.empty-state h3 {
    color: #95a5a6;
    margin-bottom: 10px;
}

/* Employee Details */
.employee-details {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 30px;
    align-items: start;
}

.employee-info h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.employee-info p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.children-info {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #ecf0f1;
}

.children-info h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.children-info ul {
    list-style: none;
    padding: 0;
}

.children-info li {
    padding: 8px 0;
    border-bottom: 1px solid #ecf0f1;
}

.children-info li:last-child {
    border-bottom: none;
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* Financial Services Styles */
.financial-stats {
    margin-bottom: 30px;
}

.financial-tabs {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #ecf0f1;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    color: #7f8c8d;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    border-bottom: 3px solid transparent;
}

.tab-btn:hover {
    background: #ecf0f1;
    color: #2c3e50;
}

.tab-btn.active {
    background: white;
    color: #3498db;
    border-bottom-color: #3498db;
}

.tab-content {
    display: none;
    padding: 25px;
}

.tab-content.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.tab-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.5rem;
}

/* Financial Badges */
.aid-type-badge,
.grant-type-badge,
.loan-type-badge {
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 6px;
}

.aid-type-badge {
    background: #e8f5e8;
    color: #2e7d32;
}

.grant-type-badge {
    background: #e3f2fd;
    color: #1976d2;
}

.loan-type-badge {
    background: #fff3e0;
    color: #f57c00;
}

.paid-amount {
    color: #2ecc71;
    font-weight: 600;
}

.remaining-amount {
    color: #e74c3c;
    font-weight: 600;
}

.btn-success {
    background: #2ecc71;
    color: white;
}

.btn-success:hover {
    background: #27ae60;
}

/* Responsive Design for Financial Tables */
@media (max-width: 1200px) {
    .data-table {
        font-size: 0.8rem;
    }

    .data-table th,
    .data-table td {
        padding: 8px 5px;
    }

    .action-buttons {
        flex-direction: column;
        gap: 3px;
    }

    .btn-icon {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .tab-buttons {
        flex-direction: column;
    }

    .tab-btn {
        justify-content: flex-start;
        padding: 12px 20px;
    }

    .tab-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .search-input {
        min-width: auto;
    }

    .table-container {
        overflow-x: auto;
    }

    .data-table {
        min-width: 800px;
    }
}

/* Reports Styles */
.report-filters {
    margin-bottom: 30px;
}

.filter-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.filter-card h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: 600;
    color: #2c3e50;
}

.filter-group select,
.filter-group input {
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3498db;
}

.report-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    overflow: hidden;
}

.report-section {
    padding: 25px;
    border-bottom: 1px solid #ecf0f1;
}

.report-section:last-child {
    border-bottom: none;
}

.report-section h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
}

/* Summary Grid */
.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #ecf0f1;
}

.summary-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.summary-details h4 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1rem;
}

.summary-details .amount {
    font-size: 1.3rem;
    font-weight: 700;
}

/* Breakdown Grid */
.breakdown-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.breakdown-item {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #ecf0f1;
}

.breakdown-item h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.breakdown-details {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.breakdown-amount {
    font-size: 1.4rem;
    font-weight: 700;
    color: #3498db;
}

.breakdown-count {
    font-size: 0.9rem;
    color: #7f8c8d;
}

/* Activities Timeline */
.activities-timeline {
    max-height: 400px;
    overflow-y: auto;
}

.timeline-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid #ecf0f1;
}

.timeline-item:last-child {
    border-bottom: none;
}

.timeline-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.timeline-content {
    flex: 1;
}

.timeline-content h5 {
    color: #2c3e50;
    margin-bottom: 5px;
    font-size: 1rem;
}

.timeline-content p {
    color: #7f8c8d;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.timeline-date {
    color: #95a5a6;
    font-size: 0.8rem;
}

/* Trends Info */
.trends-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #ecf0f1;
}

.trends-info p {
    color: #7f8c8d;
    margin-bottom: 20px;
    line-height: 1.6;
}

.trend-items {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.trend-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: white;
    border-radius: 8px;
    border: 1px solid #ecf0f1;
}

.trend-label {
    color: #2c3e50;
    font-weight: 500;
}

.trend-value {
    color: #3498db;
    font-weight: 600;
}

/* Stats Table */
.stats-table {
    overflow-x: auto;
}

.stat-number {
    font-weight: 600;
    color: #2c3e50;
}

.stat-percentage {
    color: #3498db;
    font-weight: 500;
}

/* Report Footer */
.report-footer {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #ecf0f1;
}

.report-info {
    text-align: center;
    color: #7f8c8d;
}

.report-info p {
    margin-bottom: 5px;
}

.report-info strong {
    color: #2c3e50;
}

/* Print Styles */
@media print {
    .header,
    .navbar,
    .footer,
    .page-actions,
    .report-filters {
        display: none !important;
    }

    .main-content {
        padding: 0 !important;
    }

    .report-content {
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }

    .report-section {
        page-break-inside: avoid;
    }

    body {
        font-size: 12px !important;
    }

    .summary-grid,
    .breakdown-grid {
        grid-template-columns: repeat(2, 1fr) !important;
    }
}

/* Coming Soon Styles */
.coming-soon {
    min-height: 60vh;
    display: flex;
    align-items: center;
    justify-content: center;
}

.coming-soon-content {
    text-align: center;
    background: white;
    padding: 50px;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    max-width: 600px;
    width: 100%;
}

.coming-soon-icon {
    font-size: 5rem;
    color: #3498db;
    margin-bottom: 30px;
}

.coming-soon-content h2 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 20px;
}

.coming-soon-content > p {
    color: #7f8c8d;
    font-size: 1.2rem;
    margin-bottom: 40px;
    line-height: 1.6;
}

.features-preview {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 40px;
    text-align: right;
}

.features-preview h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.features-preview ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.features-preview li {
    padding: 10px 0;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    align-items: center;
    gap: 15px;
    color: #2c3e50;
}

.features-preview li:last-child {
    border-bottom: none;
}

.features-preview li i {
    color: #2ecc71;
    font-size: 1.1rem;
}

.back-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.back-actions .btn {
    min-width: 180px;
}

/* Alert Info Style */
.alert-info {
    background-color: #3498db;
}

@media (max-width: 768px) {
    .coming-soon-content {
        padding: 30px 20px;
        margin: 20px;
    }

    .coming-soon-icon {
        font-size: 3rem;
    }

    .coming-soon-content h2 {
        font-size: 2rem;
    }

    .coming-soon-content > p {
        font-size: 1rem;
    }

    .back-actions {
        flex-direction: column;
        align-items: center;
    }

    .back-actions .btn {
        width: 100%;
        max-width: 250px;
    }
}

/* Workplace Management Styles */
.add-workplace-section,
.add-bank-branch-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
}

.add-workplace-section h4,
.add-bank-branch-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.input-group {
    display: flex;
    gap: 10px;
    align-items: stretch;
}

.input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group input:focus {
    outline: none;
    border-color: #3498db;
}

.workplaces-list-section h4,
.bank-branches-list-section h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.1rem;
}

.workplaces-list,
.bank-branches-list {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ecf0f1;
    border-radius: 8px;
    background: #f8f9fa;
}

.workplace-item,
.bank-branch-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #ecf0f1;
    transition: background-color 0.3s ease;
}

.workplace-item:last-child,
.bank-branch-item:last-child {
    border-bottom: none;
}

.workplace-item:hover,
.bank-branch-item:hover {
    background-color: #e9ecef;
}

.workplace-name,
.bank-branch-name {
    flex: 1;
    color: #2c3e50;
    font-weight: 500;
    font-size: 0.9rem;
    line-height: 1.4;
}

.workplace-actions,
.bank-branch-actions {
    display: flex;
    gap: 5px;
}

.workplace-actions .btn-icon,
.bank-branch-actions .btn-icon {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
}

/* Filter Controls Enhancement */
.filter-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
}

.filter-controls .btn-sm {
    padding: 8px 12px;
    font-size: 0.9rem;
    border-radius: 6px;
}

@media (max-width: 768px) {
    .input-group {
        flex-direction: column;
    }

    .workplace-item,
    .bank-branch-item {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .workplace-actions,
    .bank-branch-actions {
        justify-content: center;
    }

    .filter-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
}

/* Bank Account Validation Styles */
.error {
    border-color: #e74c3c !important;
    background-color: #fdf2f2 !important;
}

.success {
    border-color: #2ecc71 !important;
    background-color: #f0f9f0 !important;
}

/* Validation Messages */
.validation-message {
    margin-top: 5px;
    padding: 8px 12px;
    border-radius: 5px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
    animation: slideDown 0.3s ease-out;
}

.validation-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.validation-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.validation-message.success::before {
    content: "✓";
    font-weight: bold;
    color: #2ecc71;
}

.validation-message.error::before {
    content: "⚠";
    font-weight: bold;
    color: #e74c3c;
}

/* Animation for validation messages */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced input group for bank branch and account */
.input-group select,
.input-group input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-family: 'Cairo', sans-serif;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.input-group select:focus,
.input-group input:focus {
    outline: none;
    border-color: #3498db;
}

/* Form Help Text */
.form-help {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #6c757d;
    line-height: 1.4;
    font-style: italic;
}

/* Current Photo Preview */
.current-photo-preview {
    margin-top: 10px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.current-photo-preview p {
    margin: 0 0 8px 0;
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
}

.current-photo-preview img {
    display: block;
    border: 2px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Restaurant Management Styles */
.restaurant-stats {
    margin-bottom: 30px;
}

.quick-actions {
    margin-bottom: 30px;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    border: 2px solid transparent;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-color: #3498db;
}

.action-card i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 15px;
    display: block;
}

.action-card h4 {
    color: #2c3e50;
    margin: 0 0 10px 0;
    font-size: 1.2rem;
}

.action-card p {
    color: #7f8c8d;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* Section Headers */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.section-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.4rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.section-actions {
    display: flex;
    gap: 10px;
}

/* Meal Cards */
.meals-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.meal-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.meal-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.meal-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.meal-employee {
    flex: 1;
}

.employee-name {
    display: block;
    font-weight: 600;
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.employee-department {
    display: block;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.meal-type {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 5px;
}

.meal-type.lunch {
    background: #fff3cd;
    color: #856404;
}

.meal-type.dinner {
    background: #d1ecf1;
    color: #0c5460;
}

.meal-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.meal-details > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.meal-details .label {
    font-size: 0.9rem;
    color: #6c757d;
}

.meal-details .value {
    font-weight: 600;
    color: #2c3e50;
}

.meal-description {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 6px;
    font-size: 0.9rem;
    color: #495057;
    margin-bottom: 15px;
    border-left: 3px solid #3498db;
}

.meal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Subscription Cards */
.subscriptions-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 20px;
}

.subscription-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.subscription-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.subscription-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.subscription-employee {
    flex: 1;
}

.subscription-status {
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.subscription-status.نشط {
    background: #d4edda;
    color: #155724;
}

.subscription-status.معلق {
    background: #fff3cd;
    color: #856404;
}

.subscription-status.ملغي {
    background: #f8d7da;
    color: #721c24;
}

.subscription-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 15px;
}

.subscription-details > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.subscription-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Settings Section */
.settings-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.settings-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    color: #dee2e6;
    margin-bottom: 20px;
    display: block;
}

.empty-state h3 {
    color: #495057;
    margin: 0 0 10px 0;
    font-size: 1.3rem;
}

.empty-state p {
    margin: 0;
    font-size: 1rem;
    line-height: 1.5;
}

/* Cost Calculator Styles */
.cost-input-form {
    background: white;
    border-radius: 12px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.cost-results {
    margin-bottom: 30px;
}

.results-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.result-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.result-card.weekly {
    border-left: 5px solid #3498db;
}

.result-card.monthly {
    border-left: 5px solid #e74c3c;
}

.result-card.yearly {
    border-left: 5px solid #27ae60;
}

.result-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.result-card.weekly .result-icon {
    background: #3498db;
}

.result-card.monthly .result-icon {
    background: #e74c3c;
}

.result-card.yearly .result-icon {
    background: #27ae60;
}

.result-info {
    flex: 1;
}

.result-info h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.result-amount {
    margin: 0 0 5px 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.result-info small {
    color: #7f8c8d;
    font-size: 0.85rem;
}

/* Weekly Menu Styles */
.weekly-menu-display {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.weekly-menu-display h4 {
    color: #2c3e50;
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    display: flex;
    align-items: center;
    gap: 10px;
}

.menu-week-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 15px;
}

.menu-day-display {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #e9ecef;
}

.day-name {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1rem;
}

.menu-content {
    color: #6c757d;
    line-height: 1.5;
    font-size: 0.9rem;
}

/* Weekly Menu Modal Styles */
.form-description {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 25px;
    color: #1565c0;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
}

.menu-days-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
}

.menu-day-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.menu-day-card:hover {
    border-color: #3498db;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.1);
}

.day-header {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
}

.day-header i {
    color: #3498db;
    font-size: 1.2rem;
}

.day-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.menu-items textarea {
    width: 100%;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 12px;
    font-family: 'Cairo', sans-serif;
    font-size: 0.9rem;
    resize: vertical;
    transition: border-color 0.3s ease;
}

.menu-items textarea:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.menu-items textarea::placeholder {
    color: #adb5bd;
    font-style: italic;
}

/* Weekend Notice */
.weekend-notice {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-top: 20px;
    color: #856404;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.95rem;
}

.weekend-notice i {
    color: #f39c12;
    font-size: 1.1rem;
}

.weekend-notice strong {
    color: #d68910;
}

/* Responsive Design for Cost Calculator */
@media (max-width: 768px) {
    .results-grid {
        grid-template-columns: 1fr;
    }

    .menu-week-grid {
        grid-template-columns: 1fr;
    }

    .menu-days-grid {
        grid-template-columns: 1fr;
    }

    .result-card {
        flex-direction: column;
        text-align: center;
        gap: 15px;
    }

    .result-amount {
        font-size: 1.5rem;
    }
}

/* Loan Management Styles */
.loan-info-section,
.loan-details-section,
.payment-history-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.loan-info-section h4,
.loan-details-section h4,
.payment-history-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-grid,
.details-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item,
.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.detail-item.full-width {
    grid-column: 1 / -1;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.info-item label,
.detail-item label {
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.info-item span,
.detail-item span {
    color: #2c3e50;
    font-weight: 500;
}

.remaining-amount {
    color: #e74c3c !important;
    font-weight: 700 !important;
}

.paid-amount {
    color: #27ae60;
    font-weight: 600;
}

/* Financial Aid Type Badges */
.aid-type-badge,
.grant-type-badge,
.loan-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.aid-type-badge {
    background: #e8f5e8;
    color: #2e7d32;
}

.grant-type-badge {
    background: #e3f2fd;
    color: #1565c0;
}

.loan-type-badge {
    background: #fff3e0;
    color: #ef6c00;
}

/* Status Badges */
.status-badge {
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    text-align: center;
}

.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background: #f8d7da;
    color: #721c24;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 5px;
    justify-content: center;
}

.btn-icon {
    width: 32px;
    height: 32px;
    border: none;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.8rem;
}

.btn-icon:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.btn-icon.btn-primary {
    background: #007bff;
    color: white;
}

.btn-icon.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-icon.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-icon.btn-success {
    background: #28a745;
    color: white;
}

.btn-icon.btn-danger {
    background: #dc3545;
    color: white;
}

/* Financial Statistics */
.financial-stats {
    margin-bottom: 30px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 25px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-info h3 {
    margin: 0 0 5px 0;
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
}

.stat-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Financial Tabs */
.financial-tabs {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    overflow: hidden;
}

.tab-buttons {
    display: flex;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.tab-btn {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    color: #6c757d;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.tab-btn:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-btn.active {
    background: #007bff;
    color: white;
}

.tab-content {
    display: none;
    padding: 25px;
}

.tab-content.active {
    display: block;
}

.tab-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #ecf0f1;
}

.tab-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 1.3rem;
}

/* Search Input */
.search-input {
    position: relative;
    width: 300px;
}

.search-input i {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-input input {
    width: 100%;
    padding: 10px 40px 10px 15px;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease;
}

.search-input input:focus {
    outline: none;
    border-color: #007bff;
}

/* Responsive Design for Financial */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .tab-buttons {
        flex-direction: column;
    }

    .tab-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .search-input {
        width: 100%;
    }

    .info-grid,
    .details-grid {
        grid-template-columns: 1fr;
    }

    .action-buttons {
        flex-wrap: wrap;
    }
}

/* Loans Management Styles */
.loans-stats {
    margin-bottom: 30px;
}

.loan-calculation-display {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-top: 10px;
}

.calculation-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.calculation-item:last-child {
    border-bottom: none;
}

.calculation-item label {
    font-weight: 600;
    color: #495057;
}

.calculation-item span {
    font-weight: 700;
    color: #2c3e50;
    font-size: 1.1rem;
}

/* Schedule Styles */
.schedule-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 20px;
}

.schedule-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
}

.schedule-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

.schedule-header h4 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.schedule-details {
    margin-bottom: 15px;
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.schedule-item label {
    font-weight: 500;
    color: #6c757d;
    font-size: 0.9rem;
}

.schedule-item span {
    font-weight: 600;
    color: #2c3e50;
}

.schedule-actions {
    display: flex;
    justify-content: flex-end;
}

/* Employee Info in Tables */
.employee-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.employee-info strong {
    color: #2c3e50;
    font-size: 0.95rem;
}

.employee-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Disabled Button Styles */
.btn-icon:disabled,
.btn-icon[disabled] {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn-icon:disabled:hover,
.btn-icon[disabled]:hover {
    transform: none !important;
    box-shadow: none !important;
}

/* Loan Type Specific Colors */
.loan-type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
}

/* Different colors for different loan types */
.loan-type-badge:contains("قرض شخصي") {
    background: #e3f2fd;
    color: #1565c0;
}

.loan-type-badge:contains("سلفة راتب") {
    background: #f3e5f5;
    color: #7b1fa2;
}

.loan-type-badge:contains("قرض سكن") {
    background: #e8f5e8;
    color: #2e7d32;
}

.loan-type-badge:contains("قرض تعليمي") {
    background: #fff3e0;
    color: #ef6c00;
}

.loan-type-badge:contains("قرض طارئ") {
    background: #ffebee;
    color: #c62828;
}

.loan-type-badge:contains("قرض زواج") {
    background: #fce4ec;
    color: #ad1457;
}

/* Default loan type badge style */
.loan-type-badge {
    background: #f5f5f5;
    color: #424242;
}

/* Text Alignment */
.text-center {
    text-align: center;
}

/* Empty State Improvements */
.empty-state {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
}

.empty-state i {
    font-size: 3rem;
    color: #dee2e6;
    margin-bottom: 15px;
    display: block;
}

.empty-state h3 {
    color: #495057;
    margin: 0 0 8px 0;
    font-size: 1.2rem;
}

.empty-state p {
    margin: 0;
    font-size: 0.95rem;
    line-height: 1.4;
}

/* Responsive Design for Loans */
@media (max-width: 768px) {
    .schedule-grid {
        grid-template-columns: 1fr;
    }

    .calculation-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .schedule-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .employee-info {
        text-align: right;
    }
}

/* Sharia Compliance Notice */
.sharia-notice {
    background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
    border: 2px solid #4caf50;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 30px;
    color: #2e7d32;
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 1rem;
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.1);
}

.sharia-notice i {
    color: #4caf50;
    font-size: 1.3rem;
    flex-shrink: 0;
}

.sharia-notice strong {
    color: #1b5e20;
    font-weight: 700;
}

/* Responsive Design for Sharia Notice */
@media (max-width: 768px) {
    .sharia-notice {
        flex-direction: column;
        text-align: center;
        gap: 8px;
    }

    .sharia-notice i {
        font-size: 1.5rem;
    }
}

/* Health Aid Management Styles */
.health-stats {
    margin-bottom: 30px;
}

.health-requests-management {
    margin-bottom: 30px;
}

/* Request Info Section */
.request-info-section,
.request-details-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.request-info-section h4,
.request-details-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Health Aid Type Badges */
.aid-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #e8f5e8;
    color: #2e7d32;
}

/* Different colors for different aid types */
.aid-type-badge:contains("علاج طبي") {
    background: #e3f2fd;
    color: #1565c0;
}

.aid-type-badge:contains("عملية جراحية") {
    background: #ffebee;
    color: #c62828;
}

.aid-type-badge:contains("أدوية") {
    background: #f3e5f5;
    color: #7b1fa2;
}

.aid-type-badge:contains("تحاليل طبية") {
    background: #fff3e0;
    color: #ef6c00;
}

.aid-type-badge:contains("أشعة وفحوصات") {
    background: #e0f2f1;
    color: #00695c;
}

.aid-type-badge:contains("علاج أسنان") {
    background: #fce4ec;
    color: #ad1457;
}

.aid-type-badge:contains("نظارات طبية") {
    background: #e8eaf6;
    color: #3f51b5;
}

.aid-type-badge:contains("علاج طبيعي") {
    background: #e1f5fe;
    color: #0277bd;
}

.aid-type-badge:contains("أجهزة طبية") {
    background: #f1f8e9;
    color: #558b2f;
}

/* Status Badges for Health */
.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.approved {
    background: #d4edda;
    color: #155724;
}

.status-badge.rejected {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.paid {
    background: #d1ecf1;
    color: #0c5460;
}

/* Approved Amount Styling */
.approved-amount {
    color: #27ae60;
    font-weight: 600;
}

.text-muted {
    color: #6c757d;
    font-style: italic;
}

/* Health Statistics Cards */
.health-stats .stat-card.total-requests {
    border-left: 4px solid #3498db;
}

.health-stats .stat-card.approved-requests {
    border-left: 4px solid #27ae60;
}

.health-stats .stat-card.pending-requests {
    border-left: 4px solid #f39c12;
}

.health-stats .stat-card.total-amount {
    border-left: 4px solid #e74c3c;
}

/* Health Form Enhancements */
.health-requests-management .form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.health-requests-management .form-group.full-width {
    grid-column: 1 / -1;
}

/* Health Modal Enhancements */
.modal-content .details-grid .detail-item.full-width {
    grid-column: 1 / -1;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.modal-content .details-grid .detail-item.full-width span {
    background: white;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    width: 100%;
    min-height: 40px;
    line-height: 1.4;
}

/* Health Action Buttons */
.health-requests-management .action-buttons .btn-icon.btn-success {
    background: #28a745;
    color: white;
}

.health-requests-management .action-buttons .btn-icon.btn-success:hover {
    background: #218838;
}

/* Health Empty State */
.health-requests-management .empty-state {
    padding: 50px 20px;
}

.health-requests-management .empty-state i {
    color: #28a745;
    font-size: 3.5rem;
}

/* Health Quick Actions */
.health-requests-management .quick-actions .action-card:hover {
    border-color: #28a745;
}

.health-requests-management .quick-actions .action-card i {
    color: #28a745;
}

/* Responsive Design for Health */
@media (max-width: 768px) {
    .health-stats .stats-grid {
        grid-template-columns: 1fr;
    }

    .health-requests-management .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .health-requests-management .section-actions {
        flex-direction: column;
        gap: 10px;
    }

    .health-requests-management .search-input {
        width: 100%;
    }

    .request-info-section .info-grid,
    .request-details-section .details-grid {
        grid-template-columns: 1fr;
    }

    .health-requests-management .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .health-requests-management .action-buttons .btn-icon {
        width: 100%;
        justify-content: flex-start;
        padding: 8px 12px;
    }
}

/* Trips Management Styles */
.trips-stats {
    margin-bottom: 30px;
}

.trips-management {
    margin-bottom: 30px;
}

/* Trip Info Section */
.trip-info-section,
.trip-details-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.trip-info-section h4,
.trip-details-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Trip Info in Tables */
.trip-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.trip-info strong {
    color: #2c3e50;
    font-size: 0.95rem;
}

.trip-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Destination Badges */
.destination-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #e3f2fd;
    color: #1565c0;
}

/* Different colors for different destinations */
.destination-badge:contains("تونس") {
    background: #e8f5e8;
    color: #2e7d32;
}

.destination-badge:contains("تركيا") {
    background: #ffebee;
    color: #c62828;
}

.destination-badge:contains("المغرب") {
    background: #f3e5f5;
    color: #7b1fa2;
}

.destination-badge:contains("مصر") {
    background: #fff3e0;
    color: #ef6c00;
}

.destination-badge:contains("الأردن") {
    background: #e0f2f1;
    color: #00695c;
}

.destination-badge:contains("الإمارات") {
    background: #fce4ec;
    color: #ad1457;
}

/* Participants Count */
.participants-count {
    font-weight: 600;
    color: #2c3e50;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

/* Trip Status Badges */
.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.completed {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Participants Section */
.participants-section {
    margin-top: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.participants-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.participants-table {
    background: white;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.participants-table .data-table {
    margin: 0;
    border: none;
}

.participants-table .data-table th,
.participants-table .data-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
}

.participants-table .data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* Trip Statistics Cards */
.trips-stats .stat-card.total-trips {
    border-left: 4px solid #3498db;
}

.trips-stats .stat-card.active-trips {
    border-left: 4px solid #27ae60;
}

.trips-stats .stat-card.total-participants {
    border-left: 4px solid #f39c12;
}

.trips-stats .stat-card.total-revenue {
    border-left: 4px solid #e74c3c;
}

/* Trip Form Enhancements */
.trips-management .form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.trips-management .form-group.full-width {
    grid-column: 1 / -1;
}

.trips-management .form-group input[readonly] {
    background-color: #f8f9fa;
    cursor: not-allowed;
}

/* Trip Modal Enhancements */
.modal-content .details-grid .detail-item.full-width {
    grid-column: 1 / -1;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.modal-content .details-grid .detail-item.full-width span {
    background: white;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    width: 100%;
    min-height: 40px;
    line-height: 1.4;
}

/* Trip Action Buttons */
.trips-management .action-buttons .btn-icon.btn-success {
    background: #28a745;
    color: white;
}

.trips-management .action-buttons .btn-icon.btn-success:hover {
    background: #218838;
}

.trips-management .action-buttons .btn-icon.btn-warning {
    background: #ffc107;
    color: #212529;
}

.trips-management .action-buttons .btn-icon.btn-warning:hover {
    background: #e0a800;
}

/* Trip Empty State */
.trips-management .empty-state {
    padding: 50px 20px;
}

.trips-management .empty-state i {
    color: #3498db;
    font-size: 3.5rem;
}

/* Trip Quick Actions */
.trips-management .quick-actions .action-card:hover {
    border-color: #3498db;
}

.trips-management .quick-actions .action-card i {
    color: #3498db;
}

/* Responsive Design for Trips */
@media (max-width: 768px) {
    .trips-stats .stats-grid {
        grid-template-columns: 1fr;
    }

    .trips-management .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .trips-management .section-actions {
        flex-direction: column;
        gap: 10px;
    }

    .trips-management .search-input {
        width: 100%;
    }

    .trip-info-section .info-grid,
    .trip-details-section .details-grid {
        grid-template-columns: 1fr;
    }

    .trips-management .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .trips-management .action-buttons .btn-icon {
        width: 100%;
        justify-content: flex-start;
        padding: 8px 12px;
    }

    .participants-table {
        overflow-x: auto;
    }

    .participants-table .data-table {
        min-width: 600px;
    }
}

/* Events Management Styles */
.events-stats {
    margin-bottom: 30px;
}

.events-management {
    margin-bottom: 30px;
}

/* Event Info Section */
.event-info-section,
.event-details-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.event-info-section h4,
.event-details-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Event Info in Tables */
.event-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event-info strong {
    color: #2c3e50;
    font-size: 0.95rem;
}

.event-info small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Event DateTime Display */
.event-datetime {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.event-datetime strong {
    color: #2c3e50;
    font-size: 0.9rem;
}

.event-datetime small {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Event Type Badges */
.event-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #9b59b6;
    color: white;
}

/* Different colors for different event types */
.event-type-badge:contains("حفل تكريمي") {
    background: #e74c3c;
    color: white;
}

.event-type-badge:contains("مناسبة دينية") {
    background: #27ae60;
    color: white;
}

.event-type-badge:contains("مناسبة وطنية") {
    background: #3498db;
    color: white;
}

.event-type-badge:contains("حفل زفاف") {
    background: #e91e63;
    color: white;
}

.event-type-badge:contains("حفل تخرج") {
    background: #ff9800;
    color: white;
}

.event-type-badge:contains("ندوة ثقافية") {
    background: #795548;
    color: white;
}

.event-type-badge:contains("ورشة تدريبية") {
    background: #607d8b;
    color: white;
}

.event-type-badge:contains("مؤتمر") {
    background: #673ab7;
    color: white;
}

.event-type-badge:contains("حفل ترفيهي") {
    background: #ff5722;
    color: white;
}

.event-type-badge:contains("نشاط رياضي") {
    background: #4caf50;
    color: white;
}

.event-type-badge:contains("معرض") {
    background: #00bcd4;
    color: white;
}

/* Attendees Count */
.attendees-count {
    font-weight: 600;
    color: #2c3e50;
    background: #f8f9fa;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

/* Event Status Badges */
.status-badge.upcoming {
    background: #fff3cd;
    color: #856404;
}

.status-badge.confirmed {
    background: #d4edda;
    color: #155724;
}

.status-badge.ongoing {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.completed {
    background: #e2e3e5;
    color: #383d41;
}

.status-badge.cancelled {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.postponed {
    background: #ffeaa7;
    color: #6c5ce7;
}

/* Attendees Section */
.attendees-section {
    margin-top: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.attendees-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.attendees-table {
    background: white;
    border-radius: 6px;
    overflow: hidden;
    border: 1px solid #dee2e6;
}

.attendees-table .data-table {
    margin: 0;
    border: none;
}

.attendees-table .data-table th,
.attendees-table .data-table td {
    padding: 12px;
    border-bottom: 1px solid #dee2e6;
}

.attendees-table .data-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

/* Event Statistics Cards */
.events-stats .stat-card.total-events {
    border-left: 4px solid #9b59b6;
}

.events-stats .stat-card.upcoming-events {
    border-left: 4px solid #3498db;
}

.events-stats .stat-card.total-attendees {
    border-left: 4px solid #27ae60;
}

.events-stats .stat-card.total-budget {
    border-left: 4px solid #e74c3c;
}

/* Event Form Enhancements */
.events-management .form-group textarea {
    min-height: 80px;
    resize: vertical;
}

.events-management .form-group.full-width {
    grid-column: 1 / -1;
}

/* Event Modal Enhancements */
.modal-content .details-grid .detail-item.full-width {
    grid-column: 1 / -1;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
}

.modal-content .details-grid .detail-item.full-width span {
    background: white;
    padding: 10px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    width: 100%;
    min-height: 40px;
    line-height: 1.4;
}

/* Event Action Buttons */
.events-management .action-buttons .btn-icon.btn-success {
    background: #28a745;
    color: white;
}

.events-management .action-buttons .btn-icon.btn-success:hover {
    background: #218838;
}

.events-management .action-buttons .btn-icon.btn-warning {
    background: #ffc107;
    color: #212529;
}

.events-management .action-buttons .btn-icon.btn-warning:hover {
    background: #e0a800;
}

/* Event Empty State */
.events-management .empty-state {
    padding: 50px 20px;
}

.events-management .empty-state i {
    color: #9b59b6;
    font-size: 3.5rem;
}

/* Event Quick Actions */
.events-management .quick-actions .action-card:hover {
    border-color: #9b59b6;
}

.events-management .quick-actions .action-card i {
    color: #9b59b6;
}

/* Responsive Design for Events */
@media (max-width: 768px) {
    .events-stats .stats-grid {
        grid-template-columns: 1fr;
    }

    .events-management .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .events-management .section-actions {
        flex-direction: column;
        gap: 10px;
    }

    .events-management .search-input {
        width: 100%;
    }

    .event-info-section .info-grid,
    .event-details-section .details-grid {
        grid-template-columns: 1fr;
    }

    .events-management .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .events-management .action-buttons .btn-icon {
        width: 100%;
        justify-content: flex-start;
        padding: 8px 12px;
    }

    .attendees-table {
        overflow-x: auto;
    }

    .attendees-table .data-table {
        min-width: 700px;
    }

    .event-datetime {
        text-align: center;
    }
}

/* Financial Year Selector Styles */
.financial-year-selector {
    display: flex;
    align-items: center;
    gap: 10px;
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.financial-year-selector label {
    font-weight: 600;
    color: #495057;
    margin: 0;
    white-space: nowrap;
}

.financial-year-selector select {
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background: white;
    color: #495057;
    font-weight: 600;
    min-width: 80px;
}

.financial-year-selector select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Treasury Management Styles */
.financial-overview {
    margin-bottom: 30px;
}

.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.overview-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-left: 4px solid #007bff;
    display: flex;
    align-items: center;
    gap: 15px;
}

.overview-card .card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.overview-card .card-content h3 {
    margin: 0 0 5px 0;
    font-size: 1.5rem;
    font-weight: 700;
    color: #2c3e50;
}

.overview-card .card-content p {
    margin: 0 0 3px 0;
    font-weight: 600;
    color: #495057;
}

.overview-card .card-content small {
    color: #6c757d;
    font-size: 0.85rem;
}

/* Balance Colors */
.positive-balance {
    color: #28a745 !important;
}

.negative-balance {
    color: #dc3545 !important;
}

.positive-amount {
    color: #28a745;
    font-weight: 600;
}

.negative-amount {
    color: #dc3545;
    font-weight: 600;
}

/* Treasury Summary */
.treasury-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.summary-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.summary-item label {
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.summary-item span {
    font-weight: 700;
    color: #2c3e50;
}

.summary-item .current-balance.positive {
    color: #28a745;
}

.summary-item .current-balance.negative {
    color: #dc3545;
}

/* Transaction Types */
.transaction-type {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 500;
}

.transaction-type.revenue {
    background: #d4edda;
    color: #155724;
}

.transaction-type.expense {
    background: #f8d7da;
    color: #721c24;
}

/* Revenue and Expense Type Badges */
.revenue-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #28a745;
    color: white;
}

.expense-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #dc3545;
    color: white;
}

/* Status Badges for Financial */
.status-badge.confirmed {
    background: #d4edda;
    color: #155724;
}

.status-badge.pending {
    background: #fff3cd;
    color: #856404;
}

.status-badge.received {
    background: #d1ecf1;
    color: #0c5460;
}

.status-badge.postponed {
    background: #f8d7da;
    color: #721c24;
}

/* Financial Overview Cards */
.financial-overview .overview-card.total-revenue {
    border-left-color: #28a745;
}

.financial-overview .overview-card.total-expenses {
    border-left-color: #dc3545;
}

.financial-overview .overview-card.current-balance {
    border-left-color: #007bff;
}

.financial-overview .overview-card.monthly-revenue {
    border-left-color: #ffc107;
}

/* Treasury Transactions */
.treasury-transactions {
    margin-top: 20px;
}

.treasury-transactions h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Responsive Design for Financial */
@media (max-width: 768px) {
    .financial-year-selector {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .overview-grid {
        grid-template-columns: 1fr;
    }

    .treasury-summary {
        grid-template-columns: 1fr;
    }

    .financial-tabs .section-actions {
        flex-direction: column;
        gap: 10px;
    }

    .financial-tabs .section-actions .search-input {
        width: 100%;
    }
}

/* Budget Planning Styles */
.budget-overview {
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.budget-overview h4 {
    color: #2c3e50;
    margin: 0 0 20px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.budget-comparison {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.comparison-section {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.comparison-section h5 {
    color: #495057;
    margin: 0 0 15px 0;
    font-size: 1rem;
    font-weight: 600;
}

.comparison-grid {
    display: grid;
    gap: 10px;
}

.comparison-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.comparison-item:last-child {
    border-bottom: none;
}

.comparison-item label {
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.comparison-item input {
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    width: 150px;
    text-align: right;
}

.comparison-item span {
    font-weight: 600;
    color: #2c3e50;
}

.comparison-item small {
    display: block;
    color: #6c757d;
    font-size: 0.8rem;
    margin-top: 2px;
}

/* Variance Colors */
.positive-variance {
    color: #28a745 !important;
}

.negative-variance {
    color: #dc3545 !important;
}

/* Category Budgets */
.category-budgets {
    margin-top: 20px;
}

.budget-categories-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.budget-categories-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.categories-container {
    display: grid;
    gap: 15px;
}

.category-budget-item {
    background: white;
    padding: 15px;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    display: grid;
    grid-template-columns: 1fr auto;
    gap: 15px;
    align-items: center;
}

.category-info label {
    font-weight: 600;
    color: #2c3e50;
    margin: 0 0 8px 0;
    display: block;
}

.category-amounts {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.category-amounts span {
    font-size: 0.85rem;
}

.category-amounts .planned {
    color: #007bff;
}

.category-amounts .actual {
    color: #2c3e50;
    font-weight: 600;
}

.category-amounts .variance.positive {
    color: #28a745;
}

.category-amounts .variance.negative {
    color: #dc3545;
}

.category-budget-item input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    width: 150px;
    text-align: right;
}

/* Expense Breakdown Styles */
.breakdown-summary {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.breakdown-summary h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.stat-item .label {
    font-weight: 600;
    color: #495057;
}

.stat-item .value {
    font-weight: 700;
    color: #2c3e50;
}

/* Category Breakdown */
.breakdown-categories {
    display: grid;
    gap: 20px;
}

.category-breakdown {
    background: white;
    border-radius: 8px;
    border: 1px solid #dee2e6;
    overflow: hidden;
}

.category-header {
    padding: 15px 20px;
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.category-header h5 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.1rem;
}

.category-stats {
    display: flex;
    align-items: center;
    gap: 15px;
}

.category-stats .total {
    font-weight: 700;
    color: #dc3545;
    font-size: 1.1rem;
}

.category-stats .percentage {
    background: #dc3545;
    color: white;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.85rem;
    font-weight: 600;
}

.category-details {
    padding: 15px 20px;
}

.detail-stats {
    display: flex;
    gap: 20px;
    margin-bottom: 10px;
    color: #6c757d;
    font-size: 0.9rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #dc3545;
    transition: width 0.3s ease;
}

/* Category Items */
.category-items {
    border-top: 1px solid #dee2e6;
}

.btn-toggle {
    width: 100%;
    padding: 12px 20px;
    background: #f8f9fa;
    border: none;
    text-align: right;
    color: #495057;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
}

.btn-toggle:hover {
    background: #e9ecef;
}

.items-list {
    padding: 0;
}

.items-table {
    width: 100%;
    border-collapse: collapse;
}

.items-table th,
.items-table td {
    padding: 10px 15px;
    text-align: right;
    border-bottom: 1px solid #dee2e6;
}

.items-table th {
    background: #f8f9fa;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.items-table td {
    color: #2c3e50;
    font-size: 0.9rem;
}

.items-table tr:hover {
    background: #f8f9fa;
}

/* Responsive Design for Budget and Breakdown */
@media (max-width: 768px) {
    .budget-comparison {
        grid-template-columns: 1fr;
    }

    .comparison-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }

    .comparison-item input {
        width: 100%;
    }

    .category-budget-item {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .category-budget-item input {
        width: 100%;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .category-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .category-stats {
        align-self: stretch;
        justify-content: space-between;
    }

    .detail-stats {
        flex-direction: column;
        gap: 5px;
    }

    .items-table {
        font-size: 0.8rem;
    }

    .items-table th,
    .items-table td {
        padding: 8px 10px;
    }
}

/* Loans Management Styles */
.loan-type-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
    background: #007bff;
    color: white;
}

/* Different colors for different loan types */
.loan-type-badge:contains("قرض شخصي") {
    background: #6f42c1;
    color: white;
}

.loan-type-badge:contains("سلفة راتب") {
    background: #20c997;
    color: white;
}

.loan-type-badge:contains("قرض سكن") {
    background: #fd7e14;
    color: white;
}

.loan-type-badge:contains("قرض تعليمي") {
    background: #e83e8c;
    color: white;
}

.loan-type-badge:contains("قرض طارئ") {
    background: #dc3545;
    color: white;
}

.loan-type-badge:contains("قرض زواج") {
    background: #e91e63;
    color: white;
}

/* Loan Status Badges */
.status-badge.active {
    background: #d4edda;
    color: #155724;
}

.status-badge.completed {
    background: #e2e3e5;
    color: #383d41;
}

.status-badge.overdue {
    background: #f8d7da;
    color: #721c24;
}

.status-badge.cancelled {
    background: #f1f3f4;
    color: #6c757d;
}

/* Loan Amounts */
.paid-amount {
    color: #28a745;
    font-weight: 600;
}

.remaining-amount {
    color: #dc3545;
    font-weight: 600;
}

/* Loan Info Section */
.loan-info-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.loan-info-section h4 {
    color: #2c3e50;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.info-item label {
    font-weight: 600;
    color: #495057;
    margin: 0;
}

.info-item span {
    font-weight: 600;
    color: #2c3e50;
}

.info-item .remaining-amount {
    color: #dc3545;
    font-weight: 700;
    font-size: 1.1rem;
}

/* Loan Form Enhancements */
.loans-management .form-group input[readonly] {
    background: #f8f9fa;
    color: #6c757d;
    cursor: not-allowed;
}

/* Loan Action Buttons */
.loans-management .action-buttons .btn-icon.btn-success {
    background: #28a745;
    color: white;
}

.loans-management .action-buttons .btn-icon.btn-success:hover {
    background: #218838;
}

/* Loan Empty State */
.loans-management .empty-state {
    padding: 50px 20px;
}

.loans-management .empty-state i {
    color: #007bff;
    font-size: 3.5rem;
}

/* Loan Statistics Cards */
.loans-stats .stat-card.total-loans {
    border-left: 4px solid #007bff;
}

.loans-stats .stat-card.active-loans {
    border-left: 4px solid #28a745;
}

.loans-stats .stat-card.total-disbursed {
    border-left: 4px solid #dc3545;
}

.loans-stats .stat-card.total-repaid {
    border-left: 4px solid #17a2b8;
}

/* Responsive Design for Loans */
@media (max-width: 768px) {
    .loans-management .section-header {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .loans-management .section-actions {
        flex-direction: column;
        gap: 10px;
    }

    .loans-management .search-input {
        width: 100%;
    }

    .loan-info-section .info-grid {
        grid-template-columns: 1fr;
    }

    .loans-management .action-buttons {
        flex-direction: column;
        gap: 5px;
    }

    .loans-management .action-buttons .btn-icon {
        width: 100%;
        justify-content: flex-start;
        padding: 8px 12px;
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}