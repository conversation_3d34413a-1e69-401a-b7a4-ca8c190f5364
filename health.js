// Health Aid Management System

// Initialize health data when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeHealthData();
    loadEmployeeOptions();
    setupEventListeners();
    displayHealthRequests();
    updateHealthStatistics();
    setCurrentDate();
});

// Initialize health data
function initializeHealthData() {
    // Initialize health requests if not exists
    if (!localStorage.getItem('healthRequests')) {
        localStorage.setItem('healthRequests', JSON.stringify([]));
    }
}

// Load employee options for dropdowns
function loadEmployeeOptions() {
    const employees = db.getEmployees();
    const healthEmployeeSelect = document.getElementById('healthEmployee');

    if (healthEmployeeSelect) {
        healthEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            healthEmployeeSelect.appendChild(option);
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Set default date to today
    const healthRequestDateInput = document.getElementById('healthRequestDate');
    if (healthRequestDateInput) {
        healthRequestDateInput.value = new Date().toISOString().split('T')[0];
    }

    const approvalDateInput = document.getElementById('approvalDate');
    if (approvalDateInput) {
        approvalDateInput.value = new Date().toISOString().split('T')[0];
    }
}

// Set current date
function setCurrentDate() {
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const today = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        currentDateElement.textContent = today.toLocaleDateString('ar-DZ', options);
    }
}

// Save health aid request
function saveHealthAid() {
    const employeeId = document.getElementById('healthEmployee').value;
    const aidType = document.getElementById('healthAidType').value;
    const requestedAmount = parseFloat(document.getElementById('healthRequestedAmount').value);
    const requestDate = document.getElementById('healthRequestDate').value;
    const medicalCenter = document.getElementById('healthMedicalCenter').value.trim();
    const doctorName = document.getElementById('healthDoctorName').value.trim();
    const diagnosis = document.getElementById('healthDiagnosis').value.trim();
    const treatmentPlan = document.getElementById('healthTreatmentPlan').value.trim();
    const notes = document.getElementById('healthNotes').value.trim();

    // Validation
    if (!employeeId || !aidType || !requestedAmount || !requestDate || !diagnosis) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (requestedAmount <= 0) {
        showAlert('المبلغ المطلوب يجب أن يكون أكبر من صفر', 'error');
        return;
    }

    // Get employee info
    const employee = db.getEmployees().find(emp => emp.id == employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Create health request object
    const healthRequest = {
        id: Date.now(),
        employeeId: parseInt(employeeId),
        employeeName: employee.name,
        employeeDepartment: employee.department,
        aidType,
        requestedAmount,
        approvedAmount: 0,
        requestDate,
        approvalDate: null,
        medicalCenter,
        doctorName,
        diagnosis,
        treatmentPlan,
        notes,
        status: 'معلق',
        approvalNotes: '',
        createdAt: new Date().toISOString()
    };

    // Save health request
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
    healthRequests.push(healthRequest);
    localStorage.setItem('healthRequests', JSON.stringify(healthRequests));

    showAlert('تم حفظ طلب الإعانة الصحية بنجاح', 'success');
    hideModal('addHealthAidModal');
    resetHealthAidForm();
    displayHealthRequests();
    updateHealthStatistics();
}

// Reset health aid form
function resetHealthAidForm() {
    document.getElementById('addHealthAidForm').reset();
    document.getElementById('healthRequestDate').value = new Date().toISOString().split('T')[0];
}

// Display health requests
function displayHealthRequests() {
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
    const tableBody = document.getElementById('healthRequestsTableBody');

    if (!tableBody) return;

    if (healthRequests.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-file-medical fa-2x"></i>
                        <h3>لا توجد طلبات إعانة صحية</h3>
                        <p>اضغط على "طلب إعانة صحية" لبدء تسجيل الطلبات</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by creation date (newest first)
    healthRequests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    tableBody.innerHTML = healthRequests.map(request => {
        return `
            <tr>
                <td><strong>#${request.id}</strong></td>
                <td>
                    <div class="employee-info">
                        <strong>${request.employeeName}</strong>
                        <small>${request.employeeDepartment}</small>
                    </div>
                </td>
                <td>
                    <span class="aid-type-badge">${request.aidType}</span>
                </td>
                <td><strong>${request.requestedAmount.toLocaleString()} دج</strong></td>
                <td>
                    ${request.approvedAmount > 0 ?
                        `<strong class="approved-amount">${request.approvedAmount.toLocaleString()} دج</strong>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>${formatDate(request.requestDate)}</td>
                <td>
                    <span class="status-badge ${getStatusClass(request.status)}">
                        ${request.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewHealthAidDetails(${request.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${request.status === 'معلق' ? `
                            <button class="btn-icon btn-success" onclick="showApprovalModal(${request.id})" title="اعتماد الطلب">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-danger" onclick="deleteHealthRequest(${request.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get status class for styling
function getStatusClass(status) {
    switch(status) {
        case 'معتمد': return 'approved';
        case 'مرفوض': return 'rejected';
        case 'مدفوع': return 'paid';
        default: return 'pending';
    }
}

// Update health statistics
function updateHealthStatistics() {
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');

    const totalRequests = healthRequests.length;
    const approvedRequests = healthRequests.filter(req => req.status === 'معتمد' || req.status === 'مدفوع').length;
    const pendingRequests = healthRequests.filter(req => req.status === 'معلق').length;
    const totalAmount = healthRequests.reduce((sum, req) => sum + (req.approvedAmount || req.requestedAmount), 0);

    // Update DOM elements
    const totalRequestsEl = document.getElementById('totalHealthRequests');
    const approvedRequestsEl = document.getElementById('approvedHealthRequests');
    const pendingRequestsEl = document.getElementById('pendingHealthRequests');
    const totalAmountEl = document.getElementById('totalHealthAmount');

    if (totalRequestsEl) totalRequestsEl.textContent = totalRequests;
    if (approvedRequestsEl) approvedRequestsEl.textContent = approvedRequests;
    if (pendingRequestsEl) pendingRequestsEl.textContent = pendingRequests;
    if (totalAmountEl) totalAmountEl.textContent = `${totalAmount.toLocaleString()} دج`;
}

// Search health requests
function searchHealthRequests() {
    const searchTerm = document.getElementById('healthSearch').value.toLowerCase();
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');

    const filteredRequests = healthRequests.filter(request =>
        request.employeeName.toLowerCase().includes(searchTerm) ||
        request.aidType.toLowerCase().includes(searchTerm) ||
        request.diagnosis.toLowerCase().includes(searchTerm) ||
        request.medicalCenter.toLowerCase().includes(searchTerm)
    );

    displayFilteredHealthRequests(filteredRequests);
}

// Filter health requests
function filterHealthRequests() {
    const filterValue = document.getElementById('healthFilter').value;
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');

    let filteredRequests = healthRequests;

    if (filterValue) {
        filteredRequests = healthRequests.filter(request => request.status === filterValue);
    }

    displayFilteredHealthRequests(filteredRequests);
}

// Display filtered health requests
function displayFilteredHealthRequests(requests) {
    const tableBody = document.getElementById('healthRequestsTableBody');

    if (!tableBody) return;

    if (requests.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على طلبات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by creation date (newest first)
    requests.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    tableBody.innerHTML = requests.map(request => {
        return `
            <tr>
                <td><strong>#${request.id}</strong></td>
                <td>
                    <div class="employee-info">
                        <strong>${request.employeeName}</strong>
                        <small>${request.employeeDepartment}</small>
                    </div>
                </td>
                <td>
                    <span class="aid-type-badge">${request.aidType}</span>
                </td>
                <td><strong>${request.requestedAmount.toLocaleString()} دج</strong></td>
                <td>
                    ${request.approvedAmount > 0 ?
                        `<strong class="approved-amount">${request.approvedAmount.toLocaleString()} دج</strong>` :
                        '<span class="text-muted">-</span>'
                    }
                </td>
                <td>${formatDate(request.requestDate)}</td>
                <td>
                    <span class="status-badge ${getStatusClass(request.status)}">
                        ${request.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewHealthAidDetails(${request.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${request.status === 'معلق' ? `
                            <button class="btn-icon btn-success" onclick="showApprovalModal(${request.id})" title="اعتماد الطلب">
                                <i class="fas fa-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-danger" onclick="deleteHealthRequest(${request.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Format date for display
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

// Show pending requests
function showPendingRequests() {
    document.getElementById('healthFilter').value = 'معلق';
    filterHealthRequests();
    showAlert('تم عرض الطلبات المعلقة فقط', 'info');
}

// Show approved requests
function showApprovedRequests() {
    document.getElementById('healthFilter').value = 'معتمد';
    filterHealthRequests();
    showAlert('تم عرض الطلبات المعتمدة فقط', 'info');
}

// Show medical files
function showMedicalFiles() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Show insurance info
function showInsuranceInfo() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Show approval modal
function showApprovalModal(requestId) {
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
    const request = healthRequests.find(req => req.id === requestId);

    if (!request) {
        showAlert('لم يتم العثور على الطلب', 'error');
        return;
    }

    // Fill approval modal
    document.getElementById('approveRequestId').value = requestId;
    document.getElementById('approveEmployeeName').textContent = request.employeeName;
    document.getElementById('approveAidType').textContent = request.aidType;
    document.getElementById('approveRequestedAmount').textContent = `${request.requestedAmount.toLocaleString()} دج`;
    document.getElementById('approveDiagnosis').textContent = request.diagnosis;
    document.getElementById('approvedAmount').value = request.requestedAmount; // Default to requested amount
    document.getElementById('approvalDate').value = new Date().toISOString().split('T')[0];

    showModal('approveHealthAidModal');
}

// Approve health aid
function approveHealthAid() {
    const requestId = parseInt(document.getElementById('approveRequestId').value);
    const approvedAmount = parseFloat(document.getElementById('approvedAmount').value);
    const approvalDate = document.getElementById('approvalDate').value;
    const approvalNotes = document.getElementById('approvalNotes').value.trim();

    if (!approvedAmount || approvedAmount <= 0) {
        showAlert('يرجى إدخال مبلغ معتمد صحيح', 'error');
        return;
    }

    if (!approvalDate) {
        showAlert('يرجى تحديد تاريخ الاعتماد', 'error');
        return;
    }

    // Find and update request
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
    const requestIndex = healthRequests.findIndex(req => req.id === requestId);

    if (requestIndex === -1) {
        showAlert('لم يتم العثور على الطلب', 'error');
        return;
    }

    // Update request
    healthRequests[requestIndex].status = 'معتمد';
    healthRequests[requestIndex].approvedAmount = approvedAmount;
    healthRequests[requestIndex].approvalDate = approvalDate;
    healthRequests[requestIndex].approvalNotes = approvalNotes;

    // Save data
    localStorage.setItem('healthRequests', JSON.stringify(healthRequests));

    showAlert('تم اعتماد طلب الإعانة الصحية بنجاح', 'success');
    hideModal('approveHealthAidModal');
    resetApprovalForm();
    displayHealthRequests();
    updateHealthStatistics();
}

// Reject health aid
function rejectHealthAid() {
    const requestId = parseInt(document.getElementById('approveRequestId').value);
    const approvalNotes = document.getElementById('approvalNotes').value.trim();

    if (!confirm('هل أنت متأكد من رفض هذا الطلب؟')) {
        return;
    }

    // Find and update request
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
    const requestIndex = healthRequests.findIndex(req => req.id === requestId);

    if (requestIndex === -1) {
        showAlert('لم يتم العثور على الطلب', 'error');
        return;
    }

    // Update request
    healthRequests[requestIndex].status = 'مرفوض';
    healthRequests[requestIndex].approvalDate = new Date().toISOString().split('T')[0];
    healthRequests[requestIndex].approvalNotes = approvalNotes || 'تم رفض الطلب';

    // Save data
    localStorage.setItem('healthRequests', JSON.stringify(healthRequests));

    showAlert('تم رفض طلب الإعانة الصحية', 'warning');
    hideModal('approveHealthAidModal');
    resetApprovalForm();
    displayHealthRequests();
    updateHealthStatistics();
}

// Reset approval form
function resetApprovalForm() {
    document.getElementById('approveHealthAidForm').reset();
    document.getElementById('approvalDate').value = new Date().toISOString().split('T')[0];
}

// View health aid details
function viewHealthAidDetails(requestId) {
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
    const request = healthRequests.find(req => req.id === requestId);

    if (!request) {
        showAlert('لم يتم العثور على الطلب', 'error');
        return;
    }

    // Fill details modal
    document.getElementById('detailsRequestId').textContent = `#${request.id}`;
    document.getElementById('detailsEmployeeName').textContent = request.employeeName;
    document.getElementById('detailsEmployeeDepartment').textContent = request.employeeDepartment;
    document.getElementById('detailsAidType').textContent = request.aidType;
    document.getElementById('detailsRequestedAmount').textContent = `${request.requestedAmount.toLocaleString()} دج`;
    document.getElementById('detailsApprovedAmount').textContent = request.approvedAmount > 0 ?
        `${request.approvedAmount.toLocaleString()} دج` : '-';
    document.getElementById('detailsRequestDate').textContent = formatDate(request.requestDate);
    document.getElementById('detailsStatus').textContent = request.status;
    document.getElementById('detailsMedicalCenter').textContent = request.medicalCenter || '-';
    document.getElementById('detailsDoctorName').textContent = request.doctorName || '-';
    document.getElementById('detailsDiagnosis').textContent = request.diagnosis;
    document.getElementById('detailsTreatmentPlan').textContent = request.treatmentPlan || '-';
    document.getElementById('detailsNotes').textContent = request.notes || '-';

    showModal('healthAidDetailsModal');
}

// Delete health request
function deleteHealthRequest(requestId) {
    if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
        const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');
        const updatedRequests = healthRequests.filter(req => req.id !== requestId);
        localStorage.setItem('healthRequests', JSON.stringify(updatedRequests));

        showAlert('تم حذف طلب الإعانة الصحية بنجاح', 'success');
        displayHealthRequests();
        updateHealthStatistics();
    }
}

// Export health report
function exportHealthReport() {
    const healthRequests = JSON.parse(localStorage.getItem('healthRequests') || '[]');

    if (healthRequests.length === 0) {
        showAlert('لا توجد طلبات إعانة صحية لتصديرها', 'warning');
        return;
    }

    const totalRequests = healthRequests.length;
    const approvedRequests = healthRequests.filter(req => req.status === 'معتمد' || req.status === 'مدفوع').length;
    const pendingRequests = healthRequests.filter(req => req.status === 'معلق').length;
    const rejectedRequests = healthRequests.filter(req => req.status === 'مرفوض').length;
    const totalRequestedAmount = healthRequests.reduce((sum, req) => sum + req.requestedAmount, 0);
    const totalApprovedAmount = healthRequests.reduce((sum, req) => sum + (req.approvedAmount || 0), 0);

    let reportContent = `تقرير الإعانات الصحية
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

إحصائيات عامة:
- إجمالي الطلبات: ${totalRequests}
- الطلبات المعتمدة: ${approvedRequests}
- الطلبات المعلقة: ${pendingRequests}
- الطلبات المرفوضة: ${rejectedRequests}
- إجمالي المبالغ المطلوبة: ${totalRequestedAmount.toLocaleString()} دج
- إجمالي المبالغ المعتمدة: ${totalApprovedAmount.toLocaleString()} دج

تفاصيل الطلبات:
`;

    healthRequests.forEach((request, index) => {
        reportContent += `
${index + 1}. ${request.employeeName} (${request.employeeDepartment})
   رقم الطلب: #${request.id}
   نوع الإعانة: ${request.aidType}
   المبلغ المطلوب: ${request.requestedAmount.toLocaleString()} دج
   المبلغ المعتمد: ${request.approvedAmount > 0 ? request.approvedAmount.toLocaleString() + ' دج' : '-'}
   تاريخ الطلب: ${formatDate(request.requestDate)}
   الحالة: ${request.status}
   التشخيص: ${request.diagnosis}
   المركز الطبي: ${request.medicalCenter || '-'}
   الطبيب المعالج: ${request.doctorName || '-'}
   خطة العلاج: ${request.treatmentPlan || '-'}
   ملاحظات: ${request.notes || '-'}
`;
    });

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_الإعانات_الصحية_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم تصدير تقرير الإعانات الصحية بنجاح', 'success');
}

// Print health aid details
function printHealthAidDetails() {
    showAlert('سيتم تطوير ميزة الطباعة قريباً', 'info');
}
