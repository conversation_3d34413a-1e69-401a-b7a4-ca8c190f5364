<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مطعم الولاية - نظام إدارة لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-hands-helping"></i>
                    <h1>نظام إدارة لجنة الخدمات الاجتماعية</h1>
                </div>
                <div class="user-info">
                    <span class="date" id="currentDate"></span>
                    <div class="user-profile">
                        <i class="fas fa-user-circle"></i>
                        <span>مدير النظام</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="employees.html"><i class="fas fa-users"></i> الموظفين</a></li>
                <li><a href="restaurant.html" class="active"><i class="fas fa-utensils"></i> مطعم الولاية</a></li>
                <li><a href="financial.html"><i class="fas fa-money-bill-wave"></i> الخدمات المالية</a></li>
                <li><a href="loans.html"><i class="fas fa-hand-holding-usd"></i> القروض والسلفيات</a></li>
                <li><a href="trips.html"><i class="fas fa-plane"></i> الرحلات</a></li>
                <li><a href="events.html"><i class="fas fa-calendar-alt"></i> الفعاليات</a></li>
                <li><a href="health.html"><i class="fas fa-heartbeat"></i> الإعانات الصحية</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h2><i class="fas fa-utensils"></i> إدارة مطعم الولاية</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showModal('addMealModal')">
                        <i class="fas fa-plus"></i> إضافة وجبة جديدة
                    </button>
                    <button class="btn btn-secondary" onclick="showModal('mealSettingsModal')">
                        <i class="fas fa-cog"></i> إعدادات المطعم
                    </button>
                    <button class="btn btn-success" onclick="generateDailyReport()">
                        <i class="fas fa-file-alt"></i> تقرير يومي
                    </button>
                </div>
            </div>

            <!-- Restaurant Statistics -->
            <section class="restaurant-stats">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #e74c3c;">
                            <i class="fas fa-utensils"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="todayMealsCount">0</h3>
                            <p>وجبات اليوم</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #f39c12;">
                            <i class="fas fa-coins"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="todayRevenue">0 دج</h3>
                            <p>إيرادات اليوم</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #27ae60;">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="todaySubsidy">0 دج</h3>
                            <p>إعانة اللجنة اليوم</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #3498db;">
                            <i class="fas fa-calendar-week"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="weeklyMealsCount">0</h3>
                            <p>وجبات الأسبوع</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="actions-grid">
                    <div class="action-card" onclick="showTodayMeals()">
                        <i class="fas fa-calendar-day"></i>
                        <h4>وجبات اليوم</h4>
                        <p>عرض وإدارة وجبات اليوم</p>
                    </div>
                    <div class="action-card" onclick="showCostCalculator()">
                        <i class="fas fa-calculator"></i>
                        <h4>حاسبة تكلفة الإطعام</h4>
                        <p>حساب التكاليف الأسبوعية والشهرية والسنوية</p>
                    </div>
                    <div class="action-card" onclick="showEmployeeSubscriptions()">
                        <i class="fas fa-users"></i>
                        <h4>اشتراكات الموظفين</h4>
                        <p>إدارة اشتراكات الموظفين الشهرية</p>
                    </div>
                    <div class="action-card" onclick="showFinancialReport()">
                        <i class="fas fa-chart-line"></i>
                        <h4>التقارير المالية</h4>
                        <p>تقارير الإيرادات والإعانات</p>
                    </div>
                </div>
            </section>

            <!-- Today's Meals Section -->
            <section class="today-meals-section">
                <div class="section-header">
                    <h3><i class="fas fa-calendar-day"></i> وجبات اليوم - <span id="todayDate"></span></h3>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-sm" onclick="showModal('addMealModal')">
                            <i class="fas fa-plus"></i> إضافة وجبة
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="refreshTodayMeals()">
                            <i class="fas fa-sync-alt"></i> تحديث
                        </button>
                    </div>
                </div>
                <div class="meals-container" id="todayMealsContainer">
                    <!-- Today's meals will be loaded here -->
                </div>
            </section>

            <!-- Cost Calculator Section -->
            <section class="cost-calculator-section" style="display: none;" id="costCalculatorSection">
                <div class="section-header">
                    <h3><i class="fas fa-calculator"></i> حاسبة تكلفة الإطعام</h3>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-sm" onclick="showModal('weeklyMenuModal')">
                            <i class="fas fa-utensils"></i> قائمة الأسبوع
                        </button>
                        <button class="btn btn-success btn-sm" onclick="exportCostReport()">
                            <i class="fas fa-file-excel"></i> تصدير التقرير
                        </button>
                    </div>
                </div>

                <!-- Cost Input Form -->
                <div class="cost-input-form">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="beneficiaryCount">عدد الموظفين المستفيدين من وجبة الغداء</label>
                            <input type="number" id="beneficiaryCount" min="1" max="1000" placeholder="عدد الموظفين">
                        </div>
                        <div class="form-group">
                            <label for="mealCostPerPerson">تكلفة الوجبة للشخص الواحد (دج)</label>
                            <input type="number" id="mealCostPerPerson" min="0" step="50" placeholder="التكلفة بالدينار">
                        </div>
                        <div class="form-group">
                            <label for="workingDaysPerWeek">أيام العمل في الأسبوع</label>
                            <select id="workingDaysPerWeek">
                                <option value="5">5 أيام (الأحد - الخميس)</option>
                                <option value="4">4 أيام (الأحد - الأربعاء)</option>
                                <option value="3">3 أيام (الأحد - الثلاثاء)</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <button class="btn btn-primary" onclick="calculateCosts()">
                                <i class="fas fa-calculator"></i> حساب التكاليف
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Cost Results -->
                <div class="cost-results" id="costResults" style="display: none;">
                    <div class="results-grid">
                        <div class="result-card weekly">
                            <div class="result-icon">
                                <i class="fas fa-calendar-week"></i>
                            </div>
                            <div class="result-info">
                                <h4>التكلفة الأسبوعية</h4>
                                <p class="result-amount" id="weeklyCost">0 دج</p>
                                <small id="weeklyDetails">0 موظف × 0 أيام × 0 دج</small>
                            </div>
                        </div>
                        <div class="result-card monthly">
                            <div class="result-icon">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="result-info">
                                <h4>التكلفة الشهرية</h4>
                                <p class="result-amount" id="monthlyCost">0 دج</p>
                                <small id="monthlyDetails">4.33 أسبوع × 0 دج</small>
                            </div>
                        </div>
                        <div class="result-card yearly">
                            <div class="result-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div class="result-info">
                                <h4>التكلفة السنوية</h4>
                                <p class="result-amount" id="yearlyCost">0 دج</p>
                                <small id="yearlyDetails">52 أسبوع × 0 دج</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Weekly Menu Display -->
                <div class="weekly-menu-display" id="weeklyMenuDisplay" style="display: none;">
                    <h4><i class="fas fa-list"></i> قائمة الطعام الأسبوعية</h4>
                    <div class="menu-week-grid" id="menuWeekGrid">
                        <!-- Weekly menu will be displayed here -->
                    </div>
                </div>
            </section>

            <!-- Employee Subscriptions Section -->
            <section class="subscriptions-section" style="display: none;" id="subscriptionsSection">
                <div class="section-header">
                    <h3><i class="fas fa-users"></i> اشتراكات الموظفين</h3>
                    <div class="section-actions">
                        <button class="btn btn-primary btn-sm" onclick="showModal('addSubscriptionModal')">
                            <i class="fas fa-plus"></i> إضافة اشتراك
                        </button>
                        <button class="btn btn-success btn-sm" onclick="generateSubscriptionReport()">
                            <i class="fas fa-file-excel"></i> تصدير
                        </button>
                    </div>
                </div>
                <div class="subscriptions-container" id="subscriptionsContainer">
                    <!-- Employee subscriptions will be loaded here -->
                </div>
            </section>
        </div>
    </main>

    <!-- Add Meal Modal -->
    <div id="addMealModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> إضافة وجبة جديدة</h3>
                <span class="close" onclick="hideModal('addMealModal')">&times;</span>
            </div>
            <form id="addMealForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="mealEmployee">الموظف *</label>
                        <select id="mealEmployee" name="employee" required>
                            <option value="">اختر الموظف</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="mealDate">تاريخ الوجبة *</label>
                        <input type="date" id="mealDate" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="mealType">نوع الوجبة *</label>
                        <select id="mealType" name="type" required>
                            <option value="">اختر نوع الوجبة</option>
                            <option value="غداء">غداء</option>
                            <option value="عشاء">عشاء</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="mealPrice">سعر الوجبة الكامل (دج) *</label>
                        <input type="number" id="mealPrice" name="price" min="0" step="50" required>
                    </div>
                    <div class="form-group">
                        <label for="employeePayment">مساهمة الموظف (دج) *</label>
                        <input type="number" id="employeePayment" name="employeePayment" min="0" step="10" required>
                    </div>
                    <div class="form-group">
                        <label for="subsidyAmount">إعانة اللجنة (دج)</label>
                        <input type="number" id="subsidyAmount" name="subsidyAmount" readonly>
                    </div>
                    <div class="form-group full-width">
                        <label for="mealDescription">وصف الوجبة</label>
                        <textarea id="mealDescription" name="description" rows="2" placeholder="وصف الوجبة أو الملاحظات"></textarea>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addMealModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveMeal()">حفظ الوجبة</button>
            </div>
        </div>
    </div>

    <!-- Add Subscription Modal -->
    <div id="addSubscriptionModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-calendar-plus"></i> إضافة اشتراك شهري</h3>
                <span class="close" onclick="hideModal('addSubscriptionModal')">&times;</span>
            </div>
            <form id="addSubscriptionForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="subscriptionEmployee">الموظف *</label>
                        <select id="subscriptionEmployee" name="employee" required>
                            <option value="">اختر الموظف</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="subscriptionMonth">الشهر *</label>
                        <input type="month" id="subscriptionMonth" name="month" required>
                    </div>
                    <div class="form-group">
                        <label for="subscriptionDays">عدد أيام الاشتراك *</label>
                        <input type="number" id="subscriptionDays" name="days" min="1" max="31" required>
                    </div>
                    <div class="form-group">
                        <label for="dailyEmployeePayment">مساهمة الموظف اليومية (دج) *</label>
                        <input type="number" id="dailyEmployeePayment" name="dailyPayment" min="0" step="10" required>
                    </div>
                    <div class="form-group">
                        <label for="totalEmployeePayment">إجمالي مساهمة الموظف (دج)</label>
                        <input type="number" id="totalEmployeePayment" name="totalPayment" readonly>
                    </div>
                    <div class="form-group">
                        <label for="subscriptionStatus">حالة الاشتراك</label>
                        <select id="subscriptionStatus" name="status">
                            <option value="نشط">نشط</option>
                            <option value="معلق">معلق</option>
                            <option value="ملغي">ملغي</option>
                        </select>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addSubscriptionModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSubscription()">حفظ الاشتراك</button>
            </div>
        </div>
    </div>

    <!-- Weekly Menu Modal -->
    <div id="weeklyMenuModal" class="modal">
        <div class="modal-content" style="max-width: 800px;">
            <div class="modal-header">
                <h3><i class="fas fa-utensils"></i> إدارة قائمة الطعام الأسبوعية</h3>
                <span class="close" onclick="hideModal('weeklyMenuModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="weekly-menu-form">
                    <p class="form-description">
                        <i class="fas fa-info-circle"></i>
                        قم بإدخال قائمة الطعام لكل يوم من أيام الأسبوع. هذه القائمة ستتكرر أسبوعياً.
                    </p>

                    <div class="menu-days-grid">
                        <div class="menu-day-card">
                            <div class="day-header">
                                <i class="fas fa-calendar-day"></i>
                                <h4>الأحد</h4>
                            </div>
                            <div class="menu-items">
                                <textarea id="sundayMenu" placeholder="مثال: معكرونة، لحم مفروم، سلطة مشكلة، خبز، عصير" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="menu-day-card">
                            <div class="day-header">
                                <i class="fas fa-calendar-day"></i>
                                <h4>الاثنين</h4>
                            </div>
                            <div class="menu-items">
                                <textarea id="mondayMenu" placeholder="مثال: كسكس، خضار، لحم، سلطة، خبز، حلوى" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="menu-day-card">
                            <div class="day-header">
                                <i class="fas fa-calendar-day"></i>
                                <h4>الثلاثاء</h4>
                            </div>
                            <div class="menu-items">
                                <textarea id="tuesdayMenu" placeholder="مثال: أرز بالخضار، سمك مقلي، سلطة، خبز، فاكهة" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="menu-day-card">
                            <div class="day-header">
                                <i class="fas fa-calendar-day"></i>
                                <h4>الأربعاء</h4>
                            </div>
                            <div class="menu-items">
                                <textarea id="wednesdayMenu" placeholder="مثال: برغل، دجاج بالصلصة، سلطة، خبز، مشروب" rows="3"></textarea>
                            </div>
                        </div>

                        <div class="menu-day-card">
                            <div class="day-header">
                                <i class="fas fa-calendar-day"></i>
                                <h4>الخميس</h4>
                            </div>
                            <div class="menu-items">
                                <textarea id="thursdayMenu" placeholder="مثال: أرز أصفر، لحم مطبوخ، خضار، خبز، حلوى" rows="3"></textarea>
                            </div>
                        </div>
                    </div>

                    <div class="weekend-notice">
                        <i class="fas fa-info-circle"></i>
                        <strong>ملاحظة:</strong> المطعم مغلق يومي السبت والجمعة (عطلة أسبوعية)
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('weeklyMenuModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveWeeklyMenu()">حفظ القائمة</button>
            </div>
        </div>
    </div>

    <!-- Meal Settings Modal -->
    <div id="mealSettingsModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3><i class="fas fa-cog"></i> إعدادات المطعم</h3>
                <span class="close" onclick="hideModal('mealSettingsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div class="settings-section">
                    <h4><i class="fas fa-money-bill-wave"></i> الأسعار الافتراضية</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="defaultMealPrice">سعر الوجبة الافتراضي (دج)</label>
                            <input type="number" id="defaultMealPrice" min="0" step="50" value="300">
                        </div>
                        <div class="form-group">
                            <label for="defaultEmployeePayment">مساهمة الموظف الافتراضية (دج)</label>
                            <input type="number" id="defaultEmployeePayment" min="0" step="10" value="100">
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h4><i class="fas fa-clock"></i> أوقات الوجبات</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="lunchStartTime">بداية وقت الغداء</label>
                            <input type="time" id="lunchStartTime" value="12:00">
                        </div>
                        <div class="form-group">
                            <label for="lunchEndTime">نهاية وقت الغداء</label>
                            <input type="time" id="lunchEndTime" value="14:00">
                        </div>
                        <div class="form-group">
                            <label for="dinnerStartTime">بداية وقت العشاء</label>
                            <input type="time" id="dinnerStartTime" value="19:00">
                        </div>
                        <div class="form-group">
                            <label for="dinnerEndTime">نهاية وقت العشاء</label>
                            <input type="time" id="dinnerEndTime" value="21:00">
                        </div>
                    </div>
                </div>

                <div class="settings-section">
                    <h4><i class="fas fa-percentage"></i> نسب الإعانة</h4>
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="subsidyPercentage">نسبة إعانة اللجنة (%)</label>
                            <input type="number" id="subsidyPercentage" min="0" max="100" step="5" value="67">
                        </div>
                        <div class="form-group">
                            <label for="maxSubsidyAmount">الحد الأقصى للإعانة اليومية (دج)</label>
                            <input type="number" id="maxSubsidyAmount" min="0" step="50" value="200">
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('mealSettingsModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveRestaurantSettings()">حفظ الإعدادات</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 نظام إدارة لجنة الخدمات الاجتماعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="database.js"></script>
    <script src="script.js"></script>
    <script src="restaurant.js"></script>
</body>
</html>
