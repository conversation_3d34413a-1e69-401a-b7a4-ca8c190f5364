// الوظائف الأساسية للنظام
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

// تهيئة الصفحة
function initializePage() {
    updateCurrentDate();
    loadDashboardData();
    loadRecentActivities();
    updateFinancialSummary();
}

// تحديث التاريخ الحالي
function updateCurrentDate() {
    const dateElement = document.getElementById('currentDate');
    if (dateElement) {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric' 
        };
        dateElement.textContent = now.toLocaleDateString('ar-SA', options);
    }
}

// تحميل بيانات لوحة التحكم
function loadDashboardData() {
    const stats = db.getStatistics();
    
    // تحديث الإحصائيات
    updateElement('totalEmployees', stats.totalEmployees);
    updateElement('totalBudget', db.formatCurrency(stats.totalBudget));
    updateElement('totalAids', stats.totalAids);
    updateElement('totalTrips', stats.totalTrips);
}

// تحميل الأنشطة الأخيرة
function loadRecentActivities() {
    const activitiesContainer = document.getElementById('recentActivities');
    if (!activitiesContainer) return;

    const activities = db.getActivities().slice(0, 5); // آخر 5 أنشطة
    
    if (activities.length === 0) {
        activitiesContainer.innerHTML = `
            <div class="activity-item">
                <div class="activity-icon" style="background: #95a5a6;">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div class="activity-info">
                    <h4>لا توجد أنشطة حديثة</h4>
                    <p>ابدأ باستخدام النظام لرؤية الأنشطة هنا</p>
                </div>
                <div class="activity-time">الآن</div>
            </div>
        `;
        return;
    }

    activitiesContainer.innerHTML = activities.map(activity => `
        <div class="activity-item fade-in">
            <div class="activity-icon" style="background: ${activity.color};">
                <i class="fas fa-${activity.icon}"></i>
            </div>
            <div class="activity-info">
                <h4>${activity.title}</h4>
                <p>${activity.description}</p>
            </div>
            <div class="activity-time">${getTimeAgo(activity.date)}</div>
        </div>
    `).join('');
}

// تحديث الملخص المالي
function updateFinancialSummary() {
    const financialAids = db.getFinancialAids();
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // حساب الإيرادات والمصروفات للشهر الحالي
    const monthlyData = financialAids.filter(aid => {
        const aidDate = new Date(aid.createdAt);
        return aidDate.getMonth() === currentMonth && aidDate.getFullYear() === currentYear;
    });
    
    const monthlyIncome = monthlyData
        .filter(aid => aid.type === 'إيراد')
        .reduce((sum, aid) => sum + (aid.amount || 0), 0);
    
    const monthlyExpenses = monthlyData
        .filter(aid => aid.type !== 'إيراد')
        .reduce((sum, aid) => sum + (aid.amount || 0), 0);
    
    const monthlyBalance = monthlyIncome - monthlyExpenses;
    
    updateElement('monthlyIncome', db.formatCurrency(monthlyIncome));
    updateElement('monthlyExpenses', db.formatCurrency(monthlyExpenses));
    updateElement('monthlyBalance', db.formatCurrency(monthlyBalance));
    
    // تغيير لون الرصيد حسب القيمة
    const balanceElement = document.getElementById('monthlyBalance');
    if (balanceElement) {
        balanceElement.className = 'amount ' + (monthlyBalance >= 0 ? 'positive' : 'negative');
    }
}

// تحديث عنصر HTML
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) {
        element.textContent = value;
    }
}

// حساب الوقت المنقضي
function getTimeAgo(dateString) {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'منذ لحظات';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `منذ ${minutes} دقيقة`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `منذ ${hours} ساعة`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `منذ ${days} يوم`;
    }
}

// وظائف مساعدة للنماذج
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'block';
        document.body.style.overflow = 'hidden';
    }
}

function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
    }
}

// إغلاق النافذة المنبثقة عند النقر خارجها
window.onclick = function(event) {
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        if (event.target === modal) {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }
    });
}

// وظائف التحقق من صحة البيانات
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const requiredFields = form.querySelectorAll('[required]');
    let isValid = true;
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            field.classList.add('error');
            isValid = false;
        } else {
            field.classList.remove('error');
        }
    });
    
    return isValid;
}

// إظهار رسائل التنبيه
function showAlert(message, type = 'success') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'}"></i>
        <span>${message}</span>
        <button onclick="this.parentElement.remove()" class="alert-close">
            <i class="fas fa-times"></i>
        </button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentElement) {
            alertDiv.remove();
        }
    }, 5000);
}

// تصدير البيانات إلى Excel
function exportToExcel(data, filename) {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'البيانات');
    XLSX.writeFile(workbook, `${filename}.xlsx`);
}

// طباعة التقرير
function printReport(elementId) {
    const element = document.getElementById(elementId);
    if (!element) return;
    
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <!DOCTYPE html>
        <html dir="rtl" lang="ar">
        <head>
            <meta charset="UTF-8">
            <title>تقرير</title>
            <style>
                body { font-family: 'Cairo', sans-serif; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: right; }
                th { background-color: #f2f2f2; }
                .header { text-align: center; margin-bottom: 30px; }
                @media print { .no-print { display: none; } }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>نظام إدارة لجنة الخدمات الاجتماعية</h1>
                <p>تاريخ الطباعة: ${new Date().toLocaleDateString('ar-SA')}</p>
            </div>
            ${element.innerHTML}
        </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// البحث في الجداول
function searchTable(searchInputId, tableId) {
    const searchInput = document.getElementById(searchInputId);
    const table = document.getElementById(tableId);
    
    if (!searchInput || !table) return;
    
    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });
}

// تحديث الصفحة كل 5 دقائق
setInterval(() => {
    if (document.getElementById('recentActivities')) {
        loadRecentActivities();
        updateFinancialSummary();
    }
}, 300000); // 5 دقائق

// إضافة أنماط CSS للتنبيهات
const alertStyles = `
    .alert {
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        z-index: 1000;
        display: flex;
        align-items: center;
        gap: 10px;
        min-width: 300px;
        animation: slideIn 0.3s ease-out;
    }
    
    .alert-success {
        background-color: #2ecc71;
    }
    
    .alert-error {
        background-color: #e74c3c;
    }
    
    .alert-close {
        background: none;
        border: none;
        color: white;
        cursor: pointer;
        margin-right: auto;
    }
    
    .error {
        border-color: #e74c3c !important;
        background-color: #fdf2f2 !important;
    }
    
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
`;

// إضافة الأنماط إلى الصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = alertStyles;
document.head.appendChild(styleSheet);
