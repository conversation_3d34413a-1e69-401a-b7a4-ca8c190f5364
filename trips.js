// Trips Management System

// Initialize trips data when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeTripsData();
    loadEmployeeOptions();
    setupEventListeners();
    displayTrips();
    updateTripsStatistics();
    setCurrentDate();
});

// Initialize trips data
function initializeTripsData() {
    // Initialize trips if not exists
    if (!localStorage.getItem('trips')) {
        localStorage.setItem('trips', JSON.stringify([]));
    }

    // Initialize trip registrations if not exists
    if (!localStorage.getItem('tripRegistrations')) {
        localStorage.setItem('tripRegistrations', JSON.stringify([]));
    }
}

// Load employee options for dropdowns
function loadEmployeeOptions() {
    const employees = db.getEmployees();
    const registerEmployeeSelect = document.getElementById('registerEmployee');

    if (registerEmployeeSelect) {
        registerEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            registerEmployeeSelect.appendChild(option);
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Date change listeners for duration calculation
    const departureDate = document.getElementById('tripDepartureDate');
    const returnDate = document.getElementById('tripReturnDate');

    if (departureDate && returnDate) {
        departureDate.addEventListener('change', calculateTripDuration);
        returnDate.addEventListener('change', calculateTripDuration);
    }

    // Set minimum dates to today
    const today = new Date().toISOString().split('T')[0];
    if (departureDate) departureDate.min = today;
    if (returnDate) returnDate.min = today;

    const registrationDeadline = document.getElementById('tripRegistrationDeadline');
    if (registrationDeadline) registrationDeadline.min = today;
}

// Calculate trip duration
function calculateTripDuration() {
    const departureDate = document.getElementById('tripDepartureDate').value;
    const returnDate = document.getElementById('tripReturnDate').value;
    const durationField = document.getElementById('tripDuration');

    if (departureDate && returnDate && durationField) {
        const departure = new Date(departureDate);
        const returnD = new Date(returnDate);

        if (returnD > departure) {
            const diffTime = Math.abs(returnD - departure);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
            durationField.value = diffDays;
        } else {
            durationField.value = '';
        }
    }
}

// Set current date
function setCurrentDate() {
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const today = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        currentDateElement.textContent = today.toLocaleDateString('ar-DZ', options);
    }
}

// Save trip
function saveTrip() {
    const tripName = document.getElementById('tripName').value.trim();
    const destination = document.getElementById('tripDestination').value;
    const tripType = document.getElementById('tripType').value;
    const departureDate = document.getElementById('tripDepartureDate').value;
    const returnDate = document.getElementById('tripReturnDate').value;
    const duration = parseInt(document.getElementById('tripDuration').value) || 0;
    const price = parseFloat(document.getElementById('tripPrice').value);
    const maxParticipants = parseInt(document.getElementById('tripMaxParticipants').value);
    const hotel = document.getElementById('tripHotel').value.trim();
    const transport = document.getElementById('tripTransport').value;
    const meals = document.getElementById('tripMeals').value;
    const registrationDeadline = document.getElementById('tripRegistrationDeadline').value;
    const description = document.getElementById('tripDescription').value.trim();
    const program = document.getElementById('tripProgram').value.trim();
    const notes = document.getElementById('tripNotes').value.trim();

    // Validation
    if (!tripName || !destination || !tripType || !departureDate || !returnDate ||
        !price || !maxParticipants || !registrationDeadline || !description) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (new Date(returnDate) <= new Date(departureDate)) {
        showAlert('تاريخ العودة يجب أن يكون بعد تاريخ المغادرة', 'error');
        return;
    }

    if (new Date(registrationDeadline) >= new Date(departureDate)) {
        showAlert('آخر موعد للتسجيل يجب أن يكون قبل تاريخ المغادرة', 'error');
        return;
    }

    if (price <= 0) {
        showAlert('سعر الرحلة يجب أن يكون أكبر من صفر', 'error');
        return;
    }

    if (maxParticipants <= 0) {
        showAlert('الحد الأقصى للمشاركين يجب أن يكون أكبر من صفر', 'error');
        return;
    }

    // Create trip object
    const trip = {
        id: Date.now(),
        name: tripName,
        destination,
        type: tripType,
        departureDate,
        returnDate,
        duration,
        price,
        maxParticipants,
        currentParticipants: 0,
        hotel,
        transport,
        meals,
        registrationDeadline,
        description,
        program,
        notes,
        status: 'نشطة',
        createdAt: new Date().toISOString()
    };

    // Save trip
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    trips.push(trip);
    localStorage.setItem('trips', JSON.stringify(trips));

    showAlert('تم حفظ الرحلة بنجاح', 'success');
    hideModal('addTripModal');
    resetTripForm();
    displayTrips();
    updateTripsStatistics();
}

// Reset trip form
function resetTripForm() {
    document.getElementById('addTripForm').reset();
    document.getElementById('tripDuration').value = '';
}

// Display trips
function displayTrips() {
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    const tableBody = document.getElementById('tripsTableBody');

    if (!tableBody) return;

    if (trips.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-plane fa-2x"></i>
                        <h3>لا توجد رحلات مسجلة</h3>
                        <p>اضغط على "تنظيم رحلة جديدة" لبدء إضافة الرحلات</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by departure date (newest first)
    trips.sort((a, b) => new Date(b.departureDate) - new Date(a.departureDate));

    tableBody.innerHTML = trips.map(trip => {
        const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');
        const tripRegistrations = registrations.filter(reg => reg.tripId === trip.id);
        const currentParticipants = tripRegistrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);

        return `
            <tr>
                <td>
                    <div class="trip-info">
                        <strong>${trip.name}</strong>
                        <small>${trip.type}</small>
                    </div>
                </td>
                <td>
                    <span class="destination-badge">${trip.destination}</span>
                </td>
                <td>${formatDate(trip.departureDate)}</td>
                <td>${formatDate(trip.returnDate)}</td>
                <td><strong>${trip.price.toLocaleString()} دج</strong></td>
                <td>
                    <span class="participants-count">
                        ${currentParticipants}/${trip.maxParticipants}
                    </span>
                </td>
                <td>
                    <span class="status-badge ${getTripStatusClass(trip.status)}">
                        ${trip.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewTripDetails(${trip.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${trip.status === 'نشطة' ? `
                            <button class="btn-icon btn-success" onclick="showRegisterModal(${trip.id})" title="تسجيل مشارك">
                                <i class="fas fa-user-plus"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-warning" onclick="editTrip(${trip.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteTrip(${trip.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Get trip status class for styling
function getTripStatusClass(status) {
    switch(status) {
        case 'نشطة': return 'active';
        case 'مكتملة': return 'completed';
        case 'ملغية': return 'cancelled';
        default: return 'pending';
    }
}

// Update trips statistics
function updateTripsStatistics() {
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');

    const totalTrips = trips.length;
    const activeTrips = trips.filter(trip => trip.status === 'نشطة').length;
    const totalParticipants = registrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);
    const totalRevenue = registrations.reduce((sum, reg) => {
        const trip = trips.find(t => t.id === reg.tripId);
        return sum + (trip ? trip.price * (1 + (reg.companions || 0)) : 0);
    }, 0);

    // Update DOM elements
    const totalTripsEl = document.getElementById('totalTrips');
    const activeTripsEl = document.getElementById('activeTrips');
    const totalParticipantsEl = document.getElementById('totalParticipants');
    const totalRevenueEl = document.getElementById('totalRevenue');

    if (totalTripsEl) totalTripsEl.textContent = totalTrips;
    if (activeTripsEl) activeTripsEl.textContent = activeTrips;
    if (totalParticipantsEl) totalParticipantsEl.textContent = totalParticipants;
    if (totalRevenueEl) totalRevenueEl.textContent = `${totalRevenue.toLocaleString()} دج`;
}

// Search trips
function searchTrips() {
    const searchTerm = document.getElementById('tripsSearch').value.toLowerCase();
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');

    const filteredTrips = trips.filter(trip =>
        trip.name.toLowerCase().includes(searchTerm) ||
        trip.destination.toLowerCase().includes(searchTerm) ||
        trip.type.toLowerCase().includes(searchTerm) ||
        trip.description.toLowerCase().includes(searchTerm)
    );

    displayFilteredTrips(filteredTrips);
}

// Filter trips
function filterTrips() {
    const filterValue = document.getElementById('tripsFilter').value;
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');

    let filteredTrips = trips;

    if (filterValue) {
        filteredTrips = trips.filter(trip => trip.status === filterValue);
    }

    displayFilteredTrips(filteredTrips);
}

// Display filtered trips
function displayFilteredTrips(trips) {
    const tableBody = document.getElementById('tripsTableBody');

    if (!tableBody) return;

    if (trips.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="8" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على رحلات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by departure date (newest first)
    trips.sort((a, b) => new Date(b.departureDate) - new Date(a.departureDate));

    tableBody.innerHTML = trips.map(trip => {
        const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');
        const tripRegistrations = registrations.filter(reg => reg.tripId === trip.id);
        const currentParticipants = tripRegistrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);

        return `
            <tr>
                <td>
                    <div class="trip-info">
                        <strong>${trip.name}</strong>
                        <small>${trip.type}</small>
                    </div>
                </td>
                <td>
                    <span class="destination-badge">${trip.destination}</span>
                </td>
                <td>${formatDate(trip.departureDate)}</td>
                <td>${formatDate(trip.returnDate)}</td>
                <td><strong>${trip.price.toLocaleString()} دج</strong></td>
                <td>
                    <span class="participants-count">
                        ${currentParticipants}/${trip.maxParticipants}
                    </span>
                </td>
                <td>
                    <span class="status-badge ${getTripStatusClass(trip.status)}">
                        ${trip.status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewTripDetails(${trip.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${trip.status === 'نشطة' ? `
                            <button class="btn-icon btn-success" onclick="showRegisterModal(${trip.id})" title="تسجيل مشارك">
                                <i class="fas fa-user-plus"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-warning" onclick="editTrip(${trip.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteTrip(${trip.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Format date for display
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

// Show active trips
function showActiveTrips() {
    document.getElementById('tripsFilter').value = 'نشطة';
    filterTrips();
    showAlert('تم عرض الرحلات النشطة فقط', 'info');
}

// Show completed trips
function showCompletedTrips() {
    document.getElementById('tripsFilter').value = 'مكتملة';
    filterTrips();
    showAlert('تم عرض الرحلات المكتملة فقط', 'info');
}

// Show participants list
function showParticipantsList() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Show destinations
function showDestinations() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// View trip details
function viewTripDetails(tripId) {
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    const trip = trips.find(t => t.id === tripId);

    if (!trip) {
        showAlert('لم يتم العثور على الرحلة', 'error');
        return;
    }

    const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');
    const tripRegistrations = registrations.filter(reg => reg.tripId === tripId);
    const currentParticipants = tripRegistrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);

    // Fill details modal
    document.getElementById('detailsTripName').textContent = trip.name;
    document.getElementById('detailsTripDestination').textContent = trip.destination;
    document.getElementById('detailsTripType').textContent = trip.type;
    document.getElementById('detailsDepartureDate').textContent = formatDate(trip.departureDate);
    document.getElementById('detailsReturnDate').textContent = formatDate(trip.returnDate);
    document.getElementById('detailsDuration').textContent = `${trip.duration} أيام`;
    document.getElementById('detailsPrice').textContent = `${trip.price.toLocaleString()} دج`;
    document.getElementById('detailsMaxParticipants').textContent = trip.maxParticipants;
    document.getElementById('detailsCurrentParticipants').textContent = currentParticipants;
    document.getElementById('detailsHotel').textContent = trip.hotel || '-';
    document.getElementById('detailsTransport').textContent = trip.transport || '-';
    document.getElementById('detailsMeals').textContent = trip.meals || '-';
    document.getElementById('detailsRegistrationDeadline').textContent = formatDate(trip.registrationDeadline);
    document.getElementById('detailsStatus').textContent = trip.status;
    document.getElementById('detailsDescription').textContent = trip.description;
    document.getElementById('detailsProgram').textContent = trip.program || '-';
    document.getElementById('detailsNotes').textContent = trip.notes || '-';

    // Display participants list
    const participantsList = document.getElementById('participantsList');
    if (participantsList) {
        if (tripRegistrations.length === 0) {
            participantsList.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-users fa-2x"></i>
                    <p>لا توجد تسجيلات للرحلة حتى الآن</p>
                </div>
            `;
        } else {
            participantsList.innerHTML = `
                <div class="participants-table">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>الموظف</th>
                                <th>القسم</th>
                                <th>المرافقين</th>
                                <th>طريقة الدفع</th>
                                <th>الهاتف</th>
                                <th>تاريخ التسجيل</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${tripRegistrations.map(reg => `
                                <tr>
                                    <td><strong>${reg.employeeName}</strong></td>
                                    <td>${reg.employeeDepartment}</td>
                                    <td>${reg.companions || 0}</td>
                                    <td>${reg.paymentMethod}</td>
                                    <td>${reg.phone}</td>
                                    <td>${formatDate(reg.registrationDate)}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }
    }

    showModal('tripDetailsModal');
}

// Show register modal
function showRegisterModal(tripId) {
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    const trip = trips.find(t => t.id === tripId);

    if (!trip) {
        showAlert('لم يتم العثور على الرحلة', 'error');
        return;
    }

    const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');
    const tripRegistrations = registrations.filter(reg => reg.tripId === tripId);
    const currentParticipants = tripRegistrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);
    const availableSeats = trip.maxParticipants - currentParticipants;

    if (availableSeats <= 0) {
        showAlert('عذراً، لا توجد مقاعد متاحة في هذه الرحلة', 'warning');
        return;
    }

    // Check registration deadline
    const today = new Date();
    const deadline = new Date(trip.registrationDeadline);

    if (today > deadline) {
        showAlert('عذراً، انتهى موعد التسجيل لهذه الرحلة', 'warning');
        return;
    }

    // Fill register modal
    document.getElementById('registerTripId').value = tripId;
    document.getElementById('registerTripName').textContent = trip.name;
    document.getElementById('registerTripDestination').textContent = trip.destination;
    document.getElementById('registerTripPrice').textContent = `${trip.price.toLocaleString()} دج`;
    document.getElementById('registerAvailableSeats').textContent = availableSeats;

    showModal('registerTripModal');
}

// Save trip registration
function saveTripRegistration() {
    const tripId = parseInt(document.getElementById('registerTripId').value);
    const employeeId = document.getElementById('registerEmployee').value;
    const companions = parseInt(document.getElementById('registerCompanions').value) || 0;
    const paymentMethod = document.getElementById('registerPaymentMethod').value;
    const phone = document.getElementById('registerPhone').value.trim();
    const notes = document.getElementById('registerNotes').value.trim();

    if (!employeeId || !paymentMethod || !phone) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // Get employee info
    const employee = db.getEmployees().find(emp => emp.id == employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Get trip info
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    const trip = trips.find(t => t.id === tripId);
    if (!trip) {
        showAlert('لم يتم العثور على الرحلة', 'error');
        return;
    }

    // Check available seats
    const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');
    const tripRegistrations = registrations.filter(reg => reg.tripId === tripId);
    const currentParticipants = tripRegistrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);
    const availableSeats = trip.maxParticipants - currentParticipants;
    const requiredSeats = 1 + companions;

    if (requiredSeats > availableSeats) {
        showAlert(`عذراً، المقاعد المتاحة (${availableSeats}) أقل من المطلوب (${requiredSeats})`, 'error');
        return;
    }

    // Check if employee already registered
    const existingRegistration = tripRegistrations.find(reg => reg.employeeId === parseInt(employeeId));
    if (existingRegistration) {
        showAlert('هذا الموظف مسجل مسبقاً في هذه الرحلة', 'error');
        return;
    }

    // Create registration object
    const registration = {
        id: Date.now(),
        tripId: tripId,
        employeeId: parseInt(employeeId),
        employeeName: employee.name,
        employeeDepartment: employee.department,
        companions: companions,
        paymentMethod: paymentMethod,
        phone: phone,
        notes: notes,
        registrationDate: new Date().toISOString().split('T')[0],
        createdAt: new Date().toISOString()
    };

    // Save registration
    registrations.push(registration);
    localStorage.setItem('tripRegistrations', JSON.stringify(registrations));

    showAlert('تم تسجيل المشارك في الرحلة بنجاح', 'success');
    hideModal('registerTripModal');
    resetRegistrationForm();
    displayTrips();
    updateTripsStatistics();
}

// Reset registration form
function resetRegistrationForm() {
    document.getElementById('registerTripForm').reset();
}

// Edit trip
function editTrip(tripId) {
    showAlert('سيتم تطوير ميزة التعديل قريباً', 'info');
}

// Delete trip
function deleteTrip(tripId) {
    if (confirm('هل أنت متأكد من حذف هذه الرحلة؟ سيتم حذف جميع التسجيلات المرتبطة بها أيضاً.')) {
        // Delete trip
        const trips = JSON.parse(localStorage.getItem('trips') || '[]');
        const updatedTrips = trips.filter(trip => trip.id !== tripId);
        localStorage.setItem('trips', JSON.stringify(updatedTrips));

        // Delete related registrations
        const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');
        const updatedRegistrations = registrations.filter(reg => reg.tripId !== tripId);
        localStorage.setItem('tripRegistrations', JSON.stringify(updatedRegistrations));

        showAlert('تم حذف الرحلة بنجاح', 'success');
        displayTrips();
        updateTripsStatistics();
    }
}

// Export trips report
function exportTripsReport() {
    const trips = JSON.parse(localStorage.getItem('trips') || '[]');
    const registrations = JSON.parse(localStorage.getItem('tripRegistrations') || '[]');

    if (trips.length === 0) {
        showAlert('لا توجد رحلات لتصديرها', 'warning');
        return;
    }

    const totalTrips = trips.length;
    const activeTrips = trips.filter(trip => trip.status === 'نشطة').length;
    const totalParticipants = registrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);
    const totalRevenue = registrations.reduce((sum, reg) => {
        const trip = trips.find(t => t.id === reg.tripId);
        return sum + (trip ? trip.price * (1 + (reg.companions || 0)) : 0);
    }, 0);

    let reportContent = `تقرير الرحلات والاصطياف
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

إحصائيات عامة:
- إجمالي الرحلات: ${totalTrips}
- الرحلات النشطة: ${activeTrips}
- إجمالي المشاركين: ${totalParticipants}
- إجمالي الإيرادات: ${totalRevenue.toLocaleString()} دج

تفاصيل الرحلات:
`;

    trips.forEach((trip, index) => {
        const tripRegistrations = registrations.filter(reg => reg.tripId === trip.id);
        const currentParticipants = tripRegistrations.reduce((sum, reg) => sum + 1 + (reg.companions || 0), 0);

        reportContent += `
${index + 1}. ${trip.name}
   الوجهة: ${trip.destination}
   نوع الرحلة: ${trip.type}
   تاريخ المغادرة: ${formatDate(trip.departureDate)}
   تاريخ العودة: ${formatDate(trip.returnDate)}
   مدة الرحلة: ${trip.duration} أيام
   السعر: ${trip.price.toLocaleString()} دج
   المشاركين: ${currentParticipants}/${trip.maxParticipants}
   الحالة: ${trip.status}
   الفندق: ${trip.hotel || '-'}
   وسيلة النقل: ${trip.transport || '-'}
   الوجبات: ${trip.meals || '-'}
   آخر موعد للتسجيل: ${formatDate(trip.registrationDeadline)}
   الوصف: ${trip.description}
   البرنامج: ${trip.program || '-'}
   ملاحظات: ${trip.notes || '-'}
`;
    });

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_الرحلات_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم تصدير تقرير الرحلات بنجاح', 'success');
}

// Print trip details
function printTripDetails() {
    showAlert('سيتم تطوير ميزة الطباعة قريباً', 'info');
}
