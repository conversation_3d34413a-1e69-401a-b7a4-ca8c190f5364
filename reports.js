// وظائف التقارير والإحصائيات
document.addEventListener('DOMContentLoaded', function() {
    initializeReportsPage();
});

// تهيئة صفحة التقارير
function initializeReportsPage() {
    updateCurrentDate();
    setReportDate();
    generateReport();
}

// تعيين تاريخ التقرير
function setReportDate() {
    const reportDateElement = document.getElementById('reportDate');
    if (reportDateElement) {
        const now = new Date();
        reportDateElement.textContent = now.toLocaleDateString('ar-SA', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    }
}

// تحديث نوع التقرير
function updateReportType() {
    const reportType = document.getElementById('reportType').value;
    
    // إخفاء/إظهار الأقسام حسب نوع التقرير
    const sections = {
        'financial': ['financialSummary', 'financialBreakdown'],
        'employees': ['employeeStats'],
        'activities': ['recentActivitiesReport'],
        'comprehensive': ['financialSummary', 'employeeStats', 'financialBreakdown', 'recentActivitiesReport', 'monthlyTrends']
    };
    
    // إخفاء جميع الأقسام
    const allSections = document.querySelectorAll('.report-section');
    allSections.forEach(section => {
        section.style.display = 'none';
    });
    
    // إظهار الأقسام المطلوبة
    if (sections[reportType]) {
        sections[reportType].forEach(sectionId => {
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.display = 'block';
            }
        });
    }
    
    generateReport();
}

// تحديث الفترة الزمنية
function updateReportPeriod() {
    const period = document.getElementById('reportPeriod').value;
    const customDateRange = document.getElementById('customDateRange');
    const customDateRange2 = document.getElementById('customDateRange2');
    
    if (period === 'custom') {
        customDateRange.style.display = 'block';
        customDateRange2.style.display = 'block';
    } else {
        customDateRange.style.display = 'none';
        customDateRange2.style.display = 'none';
    }
    
    generateReport();
}

// إنشاء التقرير
function generateReport() {
    loadFinancialSummary();
    loadEmployeeStatistics();
    loadFinancialBreakdown();
    loadRecentActivities();
    loadMonthlyTrends();
}

// تحميل الملخص المالي
function loadFinancialSummary() {
    const financialAids = db.getFinancialAids();
    const period = getSelectedPeriod();
    
    // فلترة البيانات حسب الفترة المحددة
    const filteredAids = filterByPeriod(financialAids, period);
    
    // حساب الإيرادات والمصروفات
    const revenue = filteredAids
        .filter(aid => aid.category === 'revenue')
        .reduce((sum, aid) => sum + (aid.amount || 0), 0);
    
    const expenses = filteredAids
        .filter(aid => aid.category !== 'revenue')
        .reduce((sum, aid) => sum + (aid.amount || 0), 0);
    
    const netBalance = revenue - expenses;
    
    // تحديث العناصر
    updateElement('totalRevenue', db.formatCurrency(revenue));
    updateElement('totalExpenses', db.formatCurrency(expenses));
    updateElement('netBalance', db.formatCurrency(netBalance));
    
    // تغيير لون الرصيد الصافي
    const netBalanceElement = document.getElementById('netBalance');
    if (netBalanceElement) {
        netBalanceElement.className = 'amount ' + (netBalance >= 0 ? 'positive' : 'negative');
    }
}

// تحميل إحصائيات الموظفين
function loadEmployeeStatistics() {
    const employees = db.getEmployees();
    const tableBody = document.getElementById('employeeStatsBody');
    
    if (!tableBody) return;
    
    const totalEmployees = employees.length;
    const maleCount = employees.filter(emp => emp.gender === 'ذكر').length;
    const femaleCount = employees.filter(emp => emp.gender === 'أنثى').length;
    
    // حساب الأطفال حسب المستوى التعليمي
    const childrenStats = {
        'ابتدائي': 0,
        'متوسط': 0,
        'ثانوي': 0,
        'جامعي': 0
    };
    
    employees.forEach(emp => {
        if (emp.children) {
            emp.children.forEach(child => {
                if (childrenStats.hasOwnProperty(child.educationLevel)) {
                    childrenStats[child.educationLevel]++;
                }
            });
        }
    });
    
    const totalChildren = Object.values(childrenStats).reduce((sum, count) => sum + count, 0);
    
    const stats = [
        { category: 'إجمالي الموظفين', count: totalEmployees, percentage: 100 },
        { category: 'الموظفين الذكور', count: maleCount, percentage: totalEmployees ? (maleCount / totalEmployees * 100).toFixed(1) : 0 },
        { category: 'الموظفات', count: femaleCount, percentage: totalEmployees ? (femaleCount / totalEmployees * 100).toFixed(1) : 0 },
        { category: 'إجمالي الأطفال', count: totalChildren, percentage: totalEmployees ? (totalChildren / totalEmployees).toFixed(1) : 0 },
        { category: 'أطفال ابتدائي', count: childrenStats['ابتدائي'], percentage: totalChildren ? (childrenStats['ابتدائي'] / totalChildren * 100).toFixed(1) : 0 },
        { category: 'أطفال متوسط', count: childrenStats['متوسط'], percentage: totalChildren ? (childrenStats['متوسط'] / totalChildren * 100).toFixed(1) : 0 },
        { category: 'أطفال ثانوي', count: childrenStats['ثانوي'], percentage: totalChildren ? (childrenStats['ثانوي'] / totalChildren * 100).toFixed(1) : 0 },
        { category: 'أطفال جامعي', count: childrenStats['جامعي'], percentage: totalChildren ? (childrenStats['جامعي'] / totalChildren * 100).toFixed(1) : 0 }
    ];
    
    tableBody.innerHTML = stats.map(stat => `
        <tr>
            <td><strong>${stat.category}</strong></td>
            <td><span class="stat-number">${stat.count}</span></td>
            <td><span class="stat-percentage">${stat.percentage}%</span></td>
        </tr>
    `).join('');
}

// تحميل تفصيل الخدمات المالية
function loadFinancialBreakdown() {
    const financialAids = db.getFinancialAids();
    const period = getSelectedPeriod();
    const filteredAids = filterByPeriod(financialAids, period);
    
    // تجميع البيانات حسب الفئة
    const aids = filteredAids.filter(aid => aid.category === 'aid');
    const grants = filteredAids.filter(aid => aid.category === 'grant');
    const loans = filteredAids.filter(aid => aid.category === 'loan');
    
    const aidsTotal = aids.reduce((sum, aid) => sum + (aid.amount || 0), 0);
    const grantsTotal = grants.reduce((sum, grant) => sum + (grant.amount || 0), 0);
    const loansTotal = loans.reduce((sum, loan) => sum + (loan.amount || 0), 0);
    
    // تحديث العناصر
    updateElement('aidsTotal', db.formatCurrency(aidsTotal));
    updateElement('aidsCount', aids.length);
    updateElement('grantsTotal', db.formatCurrency(grantsTotal));
    updateElement('grantsCount', grants.length);
    updateElement('loansTotal', db.formatCurrency(loansTotal));
    updateElement('loansCount', loans.length);
}

// تحميل الأنشطة الأخيرة
function loadRecentActivities() {
    const activities = db.getActivities().slice(0, 10); // آخر 10 أنشطة
    const timeline = document.getElementById('activitiesTimeline');
    
    if (!timeline) return;
    
    if (activities.length === 0) {
        timeline.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-history fa-2x"></i>
                <h4>لا توجد أنشطة حديثة</h4>
                <p>لم يتم تسجيل أي أنشطة مؤخراً</p>
            </div>
        `;
        return;
    }
    
    timeline.innerHTML = activities.map(activity => `
        <div class="timeline-item">
            <div class="timeline-icon" style="background: ${activity.color};">
                <i class="fas fa-${activity.icon}"></i>
            </div>
            <div class="timeline-content">
                <h5>${activity.title}</h5>
                <p>${activity.description}</p>
                <small class="timeline-date">${getTimeAgo(activity.date)}</small>
            </div>
        </div>
    `).join('');
}

// تحميل الاتجاهات الشهرية
function loadMonthlyTrends() {
    const financialAids = db.getFinancialAids();
    const employees = db.getEmployees();
    
    // حساب المتوسطات
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();
    
    // الإعانات الشهرية
    const monthlyAids = financialAids.filter(aid => {
        const aidDate = new Date(aid.createdAt);
        return aidDate.getMonth() === currentMonth && aidDate.getFullYear() === currentYear;
    });
    
    const avgMonthlyAids = monthlyAids.reduce((sum, aid) => sum + (aid.amount || 0), 0);
    
    // المستفيدين الشهريين
    const uniqueBeneficiaries = new Set(monthlyAids.map(aid => aid.beneficiaryId));
    const avgMonthlyBeneficiaries = uniqueBeneficiaries.size;
    
    // معدل النمو (محاكاة)
    const growthRate = Math.random() * 10 - 5; // نمو عشوائي بين -5% و +5%
    
    // تحديث العناصر
    updateElement('avgMonthlyAids', db.formatCurrency(avgMonthlyAids));
    updateElement('avgMonthlyBeneficiaries', avgMonthlyBeneficiaries);
    updateElement('growthRate', growthRate.toFixed(1) + '%');
}

// الحصول على الفترة المحددة
function getSelectedPeriod() {
    const period = document.getElementById('reportPeriod').value;
    const now = new Date();
    
    switch (period) {
        case 'monthly':
            return {
                start: new Date(now.getFullYear(), now.getMonth(), 1),
                end: new Date(now.getFullYear(), now.getMonth() + 1, 0)
            };
        case 'quarterly':
            const quarter = Math.floor(now.getMonth() / 3);
            return {
                start: new Date(now.getFullYear(), quarter * 3, 1),
                end: new Date(now.getFullYear(), (quarter + 1) * 3, 0)
            };
        case 'yearly':
            return {
                start: new Date(now.getFullYear(), 0, 1),
                end: new Date(now.getFullYear(), 11, 31)
            };
        case 'custom':
            const startDate = document.getElementById('startDate').value;
            const endDate = document.getElementById('endDate').value;
            return {
                start: startDate ? new Date(startDate) : new Date(now.getFullYear(), 0, 1),
                end: endDate ? new Date(endDate) : now
            };
        default:
            return {
                start: new Date(now.getFullYear(), now.getMonth(), 1),
                end: new Date(now.getFullYear(), now.getMonth() + 1, 0)
            };
    }
}

// فلترة البيانات حسب الفترة
function filterByPeriod(data, period) {
    return data.filter(item => {
        const itemDate = new Date(item.createdAt);
        return itemDate >= period.start && itemDate <= period.end;
    });
}

// تصدير التقرير
function exportReport() {
    const reportType = document.getElementById('reportType').value;
    const period = document.getElementById('reportPeriod').value;
    
    // جمع بيانات التقرير
    const reportData = {
        type: reportType,
        period: period,
        generatedAt: new Date().toISOString(),
        data: {
            employees: db.getEmployees(),
            financialAids: db.getFinancialAids(),
            activities: db.getActivities()
        }
    };
    
    // تحويل إلى JSON وتحميل
    const jsonData = JSON.stringify(reportData, null, 2);
    const blob = new Blob([jsonData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `تقرير_${reportType}_${period}_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    showAlert('تم تصدير التقرير بنجاح', 'success');
}

// تحديث نوع التقرير عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateReportType();
});
