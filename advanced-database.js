// Advanced Local Database using IndexedDB
class AdvancedLocalDatabase {
    constructor() {
        this.dbName = 'SocialServicesDB';
        this.dbVersion = 1;
        this.db = null;
        this.isInitialized = false;
    }

    // Initialize database
    async init() {
        if (this.isInitialized) return;

        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                console.error('Database failed to open');
                reject(request.error);
            };

            request.onsuccess = () => {
                this.db = request.result;
                this.isInitialized = true;
                console.log('Database opened successfully');
                this.seedInitialData();
                resolve();
            };

            request.onupgradeneeded = (e) => {
                this.db = e.target.result;

                // Create employees object store
                if (!this.db.objectStoreNames.contains('employees')) {
                    const employeeStore = this.db.createObjectStore('employees', { keyPath: 'id', autoIncrement: true });
                    employeeStore.createIndex('employeeId', 'employeeId', { unique: true });
                    employeeStore.createIndex('name', 'name', { unique: false });
                    employeeStore.createIndex('department', 'department', { unique: false });
                    employeeStore.createIndex('status', 'status', { unique: false });
                }

                // Create departments object store
                if (!this.db.objectStoreNames.contains('departments')) {
                    const departmentStore = this.db.createObjectStore('departments', { keyPath: 'id', autoIncrement: true });
                    departmentStore.createIndex('name', 'name', { unique: true });
                }

                // Create positions object store
                if (!this.db.objectStoreNames.contains('positions')) {
                    const positionStore = this.db.createObjectStore('positions', { keyPath: 'id', autoIncrement: true });
                    positionStore.createIndex('title', 'title', { unique: false });
                    positionStore.createIndex('department', 'department', { unique: false });
                }

                // Create loans object store
                if (!this.db.objectStoreNames.contains('loans')) {
                    const loanStore = this.db.createObjectStore('loans', { keyPath: 'id', autoIncrement: true });
                    loanStore.createIndex('employeeId', 'employeeId', { unique: false });
                    loanStore.createIndex('status', 'status', { unique: false });
                    loanStore.createIndex('financialYear', 'financialYear', { unique: false });
                }

                console.log('Database setup complete');
            };
        });
    }

    // Seed initial data
    async seedInitialData() {
        try {
            // Check if data already exists
            const employeeCount = await this.countRecords('employees');
            if (employeeCount > 0) return;

            // Add initial departments
            const departments = [
                { name: 'الإدارة العامة', description: 'الإدارة العليا والتخطيط الاستراتيجي' },
                { name: 'الموارد البشرية', description: 'إدارة شؤون الموظفين والتوظيف' },
                { name: 'المالية', description: 'الشؤون المالية والمحاسبة' },
                { name: 'التسويق', description: 'التسويق والمبيعات' },
                { name: 'تقنية المعلومات', description: 'تطوير وصيانة الأنظمة' },
                { name: 'العمليات', description: 'العمليات التشغيلية' },
                { name: 'خدمة العملاء', description: 'خدمة ودعم العملاء' }
            ];

            for (const dept of departments) {
                await this.addRecord('departments', dept);
            }

            // Add initial positions
            const positions = [
                { title: 'مدير عام', department: 'الإدارة العامة', level: 'إدارة عليا' },
                { title: 'مدير', department: 'الإدارة العامة', level: 'إدارة وسطى' },
                { title: 'نائب مدير', department: 'الإدارة العامة', level: 'إدارة وسطى' },
                { title: 'أخصائي موارد بشرية', department: 'الموارد البشرية', level: 'تخصصي' },
                { title: 'محاسب', department: 'المالية', level: 'تخصصي' },
                { title: 'محاسب أول', department: 'المالية', level: 'تخصصي أول' },
                { title: 'أخصائي تسويق', department: 'التسويق', level: 'تخصصي' },
                { title: 'مطور برمجيات', department: 'تقنية المعلومات', level: 'تخصصي' },
                { title: 'مهندس نظم', department: 'تقنية المعلومات', level: 'تخصصي أول' },
                { title: 'موظف إداري', department: 'الإدارة العامة', level: 'إداري' }
            ];

            for (const pos of positions) {
                await this.addRecord('positions', pos);
            }

            console.log('Initial data seeded successfully');
        } catch (error) {
            console.error('Error seeding initial data:', error);
        }
    }

    // Generic method to add record
    async addRecord(storeName, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);

            data.createdAt = data.createdAt || new Date().toISOString();
            data.updatedAt = new Date().toISOString();

            const request = store.add(data);

            request.onsuccess = () => {
                resolve({ ...data, id: request.result });
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // Generic method to get all records
    async getAllRecords(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.getAll();

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // Generic method to get record by ID
    async getRecordById(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.get(id);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // Generic method to update record
    async updateRecord(storeName, id, data) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);

            // Get existing record first
            const getRequest = store.get(id);

            getRequest.onsuccess = () => {
                const existingRecord = getRequest.result;
                if (!existingRecord) {
                    reject(new Error('Record not found'));
                    return;
                }

                // Merge data and update timestamp
                const updatedRecord = {
                    ...existingRecord,
                    ...data,
                    id: id,
                    updatedAt: new Date().toISOString()
                };

                const putRequest = store.put(updatedRecord);

                putRequest.onsuccess = () => {
                    resolve(updatedRecord);
                };

                putRequest.onerror = () => {
                    reject(putRequest.error);
                };
            };

            getRequest.onerror = () => {
                reject(getRequest.error);
            };
        });
    }

    // Generic method to delete record
    async deleteRecord(storeName, id) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            const request = store.delete(id);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // Count records in store
    async countRecords(storeName) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const request = store.count();

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // Search records by index
    async searchByIndex(storeName, indexName, value) {
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const index = store.index(indexName);
            const request = index.getAll(value);

            request.onsuccess = () => {
                resolve(request.result);
            };

            request.onerror = () => {
                reject(request.error);
            };
        });
    }

    // Employee-specific methods
    async getEmployees() {
        return await this.getAllRecords('employees');
    }

    async getEmployeeById(id) {
        return await this.getRecordById('employees', id);
    }

    async addEmployee(employeeData) {
        // Generate unique employee ID
        const employees = await this.getEmployees();
        const maxId = employees.reduce((max, emp) => {
            const idNum = parseInt(emp.employeeId?.replace('EMP', '') || '0');
            return Math.max(max, idNum);
        }, 0);

        employeeData.employeeId = `EMP${String(maxId + 1).padStart(3, '0')}`;
        employeeData.status = employeeData.status || 'نشط';

        return await this.addRecord('employees', employeeData);
    }

    async updateEmployee(id, employeeData) {
        return await this.updateRecord('employees', id, employeeData);
    }

    async deleteEmployee(id) {
        // Check if employee has active loans
        const hasActiveLoans = await this.employeeHasActiveLoans(id);

        if (hasActiveLoans) {
            throw new Error('لا يمكن حذف الموظف لأنه لديه قروض جارية التسديد. يجب تسديد جميع القروض أولاً.');
        }

        return await this.deleteRecord('employees', id);
    }

    // Check if employee has active loans
    async employeeHasActiveLoans(employeeId) {
        try {
            const loans = await this.searchByIndex('loans', 'employeeId', employeeId);

            // Check if any loan is still active (not fully paid)
            for (const loan of loans) {
                if (loan.status === 'نشط') {
                    // Get loan payments to calculate remaining amount
                    const payments = await this.getLoanPayments(loan.id);
                    const paidAmount = payments.reduce((sum, payment) => sum + payment.amount, 0);
                    const remainingAmount = loan.amount - paidAmount;

                    if (remainingAmount > 0) {
                        return true;
                    }
                }
            }

            return false;
        } catch (error) {
            console.error('Error checking employee loans:', error);
            return false;
        }
    }

    // Loan-specific methods
    async getLoans() {
        return await this.getAllRecords('loans');
    }

    async getLoanById(id) {
        return await this.getRecordById('loans', id);
    }

    async addLoan(loanData) {
        return await this.addRecord('loans', loanData);
    }

    async updateLoan(id, loanData) {
        return await this.updateRecord('loans', id, loanData);
    }

    async deleteLoan(id) {
        return await this.deleteRecord('loans', id);
    }

    async getLoansByEmployee(employeeId) {
        return await this.searchByIndex('loans', 'employeeId', employeeId);
    }

    async getLoansByStatus(status) {
        return await this.searchByIndex('loans', 'status', status);
    }

    async getLoansByFinancialYear(year) {
        return await this.searchByIndex('loans', 'financialYear', year);
    }

    // Loan payments methods (using localStorage for now)
    async getLoanPayments(loanId) {
        const payments = JSON.parse(localStorage.getItem('loanPayments') || '[]');
        return payments.filter(payment => payment.loanId === loanId);
    }

    async addLoanPayment(paymentData) {
        const payments = JSON.parse(localStorage.getItem('loanPayments') || '[]');
        paymentData.id = Date.now();
        paymentData.createdAt = new Date().toISOString();
        payments.push(paymentData);
        localStorage.setItem('loanPayments', JSON.stringify(payments));
        return paymentData;
    }

    // Department methods
    async getDepartments() {
        return await this.getAllRecords('departments');
    }

    async addDepartment(departmentData) {
        return await this.addRecord('departments', departmentData);
    }

    async updateDepartment(id, departmentData) {
        return await this.updateRecord('departments', id, departmentData);
    }

    async deleteDepartment(id) {
        // Check if department has employees
        const employees = await this.getEmployees();
        const departmentEmployees = employees.filter(emp => emp.departmentId === id);

        if (departmentEmployees.length > 0) {
            throw new Error('لا يمكن حذف القسم لأنه يحتوي على موظفين. يجب نقل الموظفين أولاً.');
        }

        return await this.deleteRecord('departments', id);
    }

    // Position methods
    async getPositions() {
        return await this.getAllRecords('positions');
    }

    async addPosition(positionData) {
        return await this.addRecord('positions', positionData);
    }

    async updatePosition(id, positionData) {
        return await this.updateRecord('positions', id, positionData);
    }

    async deletePosition(id) {
        // Check if position has employees
        const employees = await this.getEmployees();
        const positionEmployees = employees.filter(emp => emp.positionId === id);

        if (positionEmployees.length > 0) {
            throw new Error('لا يمكن حذف المنصب لأنه مرتبط بموظفين. يجب تغيير مناصب الموظفين أولاً.');
        }

        return await this.deleteRecord('positions', id);
    }

    // Search methods
    async searchEmployees(searchTerm) {
        const employees = await this.getEmployees();
        const term = searchTerm.toLowerCase();

        return employees.filter(emp =>
            emp.name.toLowerCase().includes(term) ||
            emp.employeeId.toLowerCase().includes(term) ||
            emp.department.toLowerCase().includes(term) ||
            emp.position.toLowerCase().includes(term)
        );
    }

    // Database management methods
    async exportDatabase() {
        const data = {
            employees: await this.getEmployees(),
            departments: await this.getDepartments(),
            positions: await this.getPositions(),
            loans: await this.getLoans(),
            exportDate: new Date().toISOString()
        };

        return JSON.stringify(data, null, 2);
    }

    async importDatabase(jsonData) {
        try {
            const data = JSON.parse(jsonData);

            // Clear existing data
            await this.clearAllData();

            // Import departments first
            if (data.departments) {
                for (const dept of data.departments) {
                    delete dept.id; // Let IndexedDB assign new IDs
                    await this.addRecord('departments', dept);
                }
            }

            // Import positions
            if (data.positions) {
                for (const pos of data.positions) {
                    delete pos.id;
                    await this.addRecord('positions', pos);
                }
            }

            // Import employees
            if (data.employees) {
                for (const emp of data.employees) {
                    delete emp.id;
                    await this.addRecord('employees', emp);
                }
            }

            // Import loans
            if (data.loans) {
                for (const loan of data.loans) {
                    delete loan.id;
                    await this.addRecord('loans', loan);
                }
            }

            return true;
        } catch (error) {
            console.error('Error importing database:', error);
            throw new Error('فشل في استيراد قاعدة البيانات: ' + error.message);
        }
    }

    async clearAllData() {
        const stores = ['employees', 'departments', 'positions', 'loans'];

        for (const storeName of stores) {
            const transaction = this.db.transaction([storeName], 'readwrite');
            const store = transaction.objectStore(storeName);
            await new Promise((resolve, reject) => {
                const request = store.clear();
                request.onsuccess = () => resolve();
                request.onerror = () => reject(request.error);
            });
        }
    }
}

// Create global database instance
const advancedDB = new AdvancedLocalDatabase();

// Initialize database when page loads
document.addEventListener('DOMContentLoaded', async () => {
    try {
        await advancedDB.init();
        console.log('Advanced database initialized successfully');
    } catch (error) {
        console.error('Failed to initialize advanced database:', error);
    }
});
