<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الموظفين - نظام إدارة لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-hands-helping"></i>
                    <h1>نظام إدارة لجنة الخدمات الاجتماعية</h1>
                </div>
                <div class="user-info">
                    <span class="date" id="currentDate"></span>
                    <div class="user-profile">
                        <i class="fas fa-user-circle"></i>
                        <span>مدير النظام</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="employees.html" class="active"><i class="fas fa-users"></i> الموظفين</a></li>
                <li><a href="restaurant.html"><i class="fas fa-utensils"></i> مطعم الولاية</a></li>
                <li><a href="financial.html"><i class="fas fa-money-bill-wave"></i> الخدمات المالية</a></li>
                <li><a href="loans.html"><i class="fas fa-hand-holding-usd"></i> القروض والسلفيات</a></li>
                <li><a href="trips.html"><i class="fas fa-plane"></i> الرحلات</a></li>
                <li><a href="events.html"><i class="fas fa-calendar-alt"></i> الفعاليات</a></li>
                <li><a href="health.html"><i class="fas fa-heartbeat"></i> الإعانات الصحية</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h2><i class="fas fa-users"></i> إدارة الموظفين</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showModal('addEmployeeModal')">
                        <i class="fas fa-plus"></i> إضافة موظف جديد
                    </button>
                    <button class="btn btn-secondary" onclick="importEmployees()">
                        <i class="fas fa-file-excel"></i> استيراد من Excel
                    </button>
                    <button class="btn btn-secondary" onclick="exportEmployees()">
                        <i class="fas fa-download"></i> تصدير البيانات
                    </button>
                </div>
            </div>

            <!-- Employee Statistics -->
            <section class="employee-stats">
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #3498db;">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalEmployeesCount">0</h3>
                            <p>إجمالي الموظفين</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #e91e63;">
                            <i class="fas fa-venus"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="femaleEmployeesCount">0</h3>
                            <p>الموظفات</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #2196f3;">
                            <i class="fas fa-mars"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="maleEmployeesCount">0</h3>
                            <p>الموظفين الذكور</p>
                        </div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-icon" style="background: #ff9800;">
                            <i class="fas fa-child"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalChildrenCount">0</h3>
                            <p>إجمالي الأطفال</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Search and Filter -->
            <section class="search-filter">
                <div class="search-bar">
                    <div class="search-input">
                        <i class="fas fa-search"></i>
                        <input type="text" id="employeeSearch" placeholder="البحث عن موظف...">
                    </div>
                    <div class="filter-controls">
                        <select id="departmentFilter">
                            <option value="">جميع أماكن العمل</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                        <button class="btn btn-secondary btn-sm" onclick="showModal('manageWorkplacesModal')" title="إدارة أماكن العمل">
                            <i class="fas fa-cog"></i>
                        </button>
                        <select id="genderFilter">
                            <option value="">الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                        <button class="btn btn-secondary" onclick="clearFilters()">
                            <i class="fas fa-times"></i> مسح الفلاتر
                        </button>
                    </div>
                </div>
            </section>

            <!-- Employees Table -->
            <section class="employees-table">
                <div class="table-container">
                    <table class="data-table" id="employeesTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>الاسم الكامل</th>
                                <th>المنصب</th>
                                <th>مكان العمل</th>
                                <th>الجنس</th>
                                <th>تاريخ التوظيف</th>
                                <th>الراتب</th>
                                <th>عدد الأطفال</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="employeesTableBody">
                            <!-- Employee data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>
        </div>
    </main>

    <!-- Add Employee Modal -->
    <div id="addEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> إضافة موظف جديد</h3>
                <span class="close" onclick="hideModal('addEmployeeModal')">&times;</span>
            </div>
            <form id="addEmployeeForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="employeeName">الاسم الكامل *</label>
                        <input type="text" id="employeeName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="employeePosition">المنصب *</label>
                        <input type="text" id="employeePosition" name="position" required>
                    </div>
                    <div class="form-group">
                        <label for="employeeDepartment">مكان العمل *</label>
                        <select id="employeeDepartment" name="department" required>
                            <option value="">اختر مكان العمل</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="employeeGender">الجنس *</label>
                        <select id="employeeGender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="employeeBirthDate">تاريخ الميلاد *</label>
                        <input type="date" id="employeeBirthDate" name="birthDate" required>
                    </div>
                    <div class="form-group">
                        <label for="employeeHireDate">تاريخ التوظيف *</label>
                        <input type="date" id="employeeHireDate" name="hireDate" required>
                    </div>
                    <div class="form-group">
                        <label for="employeeSalary">الراتب (دج) *</label>
                        <input type="number" id="employeeSalary" name="salary" required>
                    </div>
                    <div class="form-group">
                        <label for="employeePhone">رقم الهاتف</label>
                        <input type="tel" id="employeePhone" name="phone">
                    </div>
                    <div class="form-group full-width">
                        <label for="employeeAddress">العنوان</label>
                        <textarea id="employeeAddress" name="address" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="employeeBankAccount">رقم الحساب البنكي</label>
                        <input type="text" id="employeeBankAccount" name="bankAccount" placeholder="20 رقم للبنوك العادية، 10+ أرقام لبريد الجزائر" maxlength="25">
                        <small class="form-help">للبنوك العادية: 20 رقم بالضبط | لبريد الجزائر: 10 أرقام أو أكثر (سيتم تحويلها تلقائياً إلى RIP)</small>
                    </div>
                    <div class="form-group">
                        <label for="employeeBankBranch">الوكالة البنكية</label>
                        <div class="input-group">
                            <select id="employeeBankBranch" name="bankBranch">
                                <option value="">اختر الوكالة البنكية</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="showModal('manageBankBranchesModal')" title="إدارة الوكالات البنكية">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group full-width">
                        <label for="employeePhoto">صورة الموظف</label>
                        <input type="file" id="employeePhoto" name="photo" accept="image/*">
                    </div>
                </div>

                <!-- Children Section -->
                <div class="children-section">
                    <h4><i class="fas fa-child"></i> بيانات الأطفال</h4>
                    <div id="childrenContainer">
                        <!-- Children will be added dynamically -->
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addChildField()">
                        <i class="fas fa-plus"></i> إضافة طفل
                    </button>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addEmployeeModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveEmployee()">حفظ</button>
            </div>
        </div>
    </div>

    <!-- Edit Employee Modal -->
    <div id="editEmployeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-edit"></i> تعديل بيانات الموظف</h3>
                <span class="close" onclick="hideModal('editEmployeeModal')">&times;</span>
            </div>
            <form id="editEmployeeForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="editEmployeeName">الاسم الكامل *</label>
                        <input type="text" id="editEmployeeName" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeePosition">المنصب *</label>
                        <input type="text" id="editEmployeePosition" name="position" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeDepartment">مكان العمل *</label>
                        <select id="editEmployeeDepartment" name="department" required>
                            <option value="">اختر مكان العمل</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeGender">الجنس *</label>
                        <select id="editEmployeeGender" name="gender" required>
                            <option value="">اختر الجنس</option>
                            <option value="ذكر">ذكر</option>
                            <option value="أنثى">أنثى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeBirthDate">تاريخ الميلاد *</label>
                        <input type="date" id="editEmployeeBirthDate" name="birthDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeHireDate">تاريخ التوظيف *</label>
                        <input type="date" id="editEmployeeHireDate" name="hireDate" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeSalary">الراتب (دج) *</label>
                        <input type="number" id="editEmployeeSalary" name="salary" min="0" step="1000" required>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeePhone">رقم الهاتف</label>
                        <input type="tel" id="editEmployeePhone" name="phone">
                    </div>
                    <div class="form-group full-width">
                        <label for="editEmployeeAddress">العنوان</label>
                        <textarea id="editEmployeeAddress" name="address" rows="2"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeBankAccount">رقم الحساب البنكي</label>
                        <input type="text" id="editEmployeeBankAccount" name="bankAccount" placeholder="20 رقم للبنوك العادية، 10+ أرقام لبريد الجزائر" maxlength="25">
                        <small class="form-help">للبنوك العادية: 20 رقم بالضبط | لبريد الجزائر: 10 أرقام أو أكثر (سيتم تحويلها تلقائياً إلى RIP)</small>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeBankBranch">الوكالة البنكية</label>
                        <div class="input-group">
                            <select id="editEmployeeBankBranch" name="bankBranch">
                                <option value="">اختر الوكالة البنكية</option>
                                <!-- Options will be loaded dynamically -->
                            </select>
                            <button type="button" class="btn btn-secondary btn-sm" onclick="showModal('manageBankBranchesModal')" title="إدارة الوكالات البنكية">
                                <i class="fas fa-cog"></i>
                            </button>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="editEmployeeStatus">حالة الموظف</label>
                        <select id="editEmployeeStatus" name="status">
                            <option value="نشط">نشط</option>
                            <option value="غير نشط">غير نشط</option>
                            <option value="مجمد">مجمد</option>
                            <option value="متقاعد">متقاعد</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="editEmployeePhoto">صورة الموظف</label>
                        <input type="file" id="editEmployeePhoto" name="photo" accept="image/*">
                        <div id="currentPhotoPreview" class="current-photo-preview" style="display: none;">
                            <p>الصورة الحالية:</p>
                            <img id="currentPhoto" src="" alt="الصورة الحالية" style="max-width: 100px; max-height: 100px; border-radius: 8px;">
                        </div>
                    </div>
                </div>

                <!-- Children Section -->
                <div class="children-section">
                    <h4><i class="fas fa-child"></i> بيانات الأطفال</h4>
                    <div id="editChildrenContainer">
                        <!-- Children will be loaded dynamically -->
                    </div>
                    <button type="button" class="btn btn-secondary" onclick="addEditChildField()">
                        <i class="fas fa-plus"></i> إضافة طفل
                    </button>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('editEmployeeModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateEmployee()">حفظ التغييرات</button>
            </div>
        </div>
    </div>

    <!-- Manage Workplaces Modal -->
    <div id="manageWorkplacesModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3><i class="fas fa-building"></i> إدارة أماكن العمل</h3>
                <span class="close" onclick="hideModal('manageWorkplacesModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Add New Workplace -->
                <div class="add-workplace-section">
                    <h4>إضافة مكان عمل جديد</h4>
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="newWorkplaceName" placeholder="اسم مكان العمل" maxlength="50">
                            <button class="btn btn-primary btn-sm" onclick="addWorkplace()">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Existing Workplaces -->
                <div class="workplaces-list-section">
                    <h4>أماكن العمل الحالية</h4>
                    <div class="workplaces-list" id="workplacesList">
                        <!-- Workplaces will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('manageWorkplacesModal')">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- Manage Bank Branches Modal -->
    <div id="manageBankBranchesModal" class="modal">
        <div class="modal-content" style="max-width: 500px;">
            <div class="modal-header">
                <h3><i class="fas fa-university"></i> إدارة الوكالات البنكية</h3>
                <span class="close" onclick="hideModal('manageBankBranchesModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Add New Bank Branch -->
                <div class="add-bank-branch-section">
                    <h4>إضافة وكالة بنكية جديدة</h4>
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="newBankBranchName" placeholder="اسم الوكالة البنكية" maxlength="100">
                            <button class="btn btn-primary btn-sm" onclick="addBankBranch()">
                                <i class="fas fa-plus"></i> إضافة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Existing Bank Branches -->
                <div class="bank-branches-list-section">
                    <h4>الوكالات البنكية الحالية</h4>
                    <div class="bank-branches-list" id="bankBranchesList">
                        <!-- Bank branches will be loaded here -->
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('manageBankBranchesModal')">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 نظام إدارة لجنة الخدمات الاجتماعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="advanced-database.js"></script>
    <script src="database-adapter.js"></script>
    <script src="script.js"></script>
    <script src="employees.js"></script>
</body>
</html>
