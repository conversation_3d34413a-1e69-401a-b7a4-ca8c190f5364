<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الخدمات المالية - نظام إدارة لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-hands-helping"></i>
                    <h1>نظام إدارة لجنة الخدمات الاجتماعية</h1>
                </div>
                <div class="user-info">
                    <span class="date" id="currentDate"></span>
                    <div class="user-profile">
                        <i class="fas fa-user-circle"></i>
                        <span>مدير النظام</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="employees.html"><i class="fas fa-users"></i> الموظفين</a></li>
                <li><a href="financial.html" class="active"><i class="fas fa-money-bill-wave"></i> الخدمات المالية</a></li>
                <li><a href="loans.html"><i class="fas fa-hand-holding-usd"></i> القروض والسلفيات</a></li>
                <li><a href="trips.html"><i class="fas fa-plane"></i> الرحلات</a></li>
                <li><a href="events.html"><i class="fas fa-calendar-alt"></i> الفعاليات</a></li>
                <li><a href="health.html"><i class="fas fa-heartbeat"></i> الإعانات الصحية</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h2><i class="fas fa-coins"></i> الإيرادات المالية والخزينة</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showModal('addRevenueModal')">
                        <i class="fas fa-plus"></i> إضافة إيراد جديد
                    </button>
                    <button class="btn btn-success" onclick="exportFinancialReport()">
                        <i class="fas fa-file-excel"></i> تصدير التقرير المالي
                    </button>
                </div>
            </div>

            <!-- Financial Overview -->
            <section class="financial-overview">
                <div class="overview-grid">
                    <div class="overview-card total-revenue">
                        <div class="card-icon" style="background: #27ae60;">
                            <i class="fas fa-arrow-up"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="totalRevenue">0 دج</h3>
                            <p>إجمالي الإيرادات</p>
                            <small>منذ بداية السنة</small>
                        </div>
                    </div>
                    <div class="overview-card total-expenses">
                        <div class="card-icon" style="background: #e74c3c;">
                            <i class="fas fa-arrow-down"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="totalExpenses">0 دج</h3>
                            <p>إجمالي المصروفات</p>
                            <small>جميع النشاطات</small>
                        </div>
                    </div>
                    <div class="overview-card current-balance">
                        <div class="card-icon" style="background: #3498db;">
                            <i class="fas fa-wallet"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="currentBalance">0 دج</h3>
                            <p>الرصيد الحالي</p>
                            <small>في الخزينة</small>
                        </div>
                    </div>
                    <div class="overview-card monthly-revenue">
                        <div class="card-icon" style="background: #f39c12;">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="card-content">
                            <h3 id="monthlyRevenue">0 دج</h3>
                            <p>إيرادات الشهر</p>
                            <small id="currentMonth">-</small>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="actions-grid">
                    <div class="action-card" onclick="showRevenueBreakdown()">
                        <i class="fas fa-chart-pie"></i>
                        <h4>تفصيل الإيرادات</h4>
                        <p>عرض مصادر الإيرادات بالتفصيل</p>
                    </div>
                    <div class="action-card" onclick="showExpenseBreakdown()">
                        <i class="fas fa-chart-bar"></i>
                        <h4>تفصيل المصروفات</h4>
                        <p>عرض المصروفات حسب النشاط</p>
                    </div>
                    <div class="action-card" onclick="showMonthlyReport()">
                        <i class="fas fa-calendar-check"></i>
                        <h4>التقرير الشهري</h4>
                        <p>تقرير مالي شهري مفصل</p>
                    </div>
                    <div class="action-card" onclick="showBudgetPlanning()">
                        <i class="fas fa-calculator"></i>
                        <h4>تخطيط الميزانية</h4>
                        <p>تخطيط وإدارة الميزانية</p>
                    </div>
                </div>
            </section>

            <!-- Financial Tabs -->
            <section class="financial-tabs">
                <div class="tab-buttons">
                    <button class="tab-btn active" onclick="showTab('revenues')">
                        <i class="fas fa-arrow-up"></i> الإيرادات
                    </button>
                    <button class="tab-btn" onclick="showTab('expenses')">
                        <i class="fas fa-arrow-down"></i> المصروفات
                    </button>
                    <button class="tab-btn" onclick="showTab('loans')">
                        <i class="fas fa-hand-holding-usd"></i> القروض والسلفيات
                    </button>
                    <button class="tab-btn" onclick="showTab('treasury')">
                        <i class="fas fa-vault"></i> الخزينة
                    </button>
                </div>

                <!-- Revenues Tab -->
                <div id="revenues-tab" class="tab-content active">
                    <div class="tab-header">
                        <h3>مصادر الإيرادات</h3>
                        <div class="section-actions">
                            <div class="search-input">
                                <input type="text" id="revenueSearch" placeholder="البحث في الإيرادات..." onkeyup="searchRevenues()">
                                <i class="fas fa-search"></i>
                            </div>
                            <select id="revenueFilter" onchange="filterRevenues()">
                                <option value="">جميع المصادر</option>
                                <option value="اشتراكات الموظفين">اشتراكات الموظفين</option>
                                <option value="إيرادات المطعم">إيرادات المطعم</option>
                                <option value="إيرادات الرحلات">إيرادات الرحلات</option>
                                <option value="إيرادات الفعاليات">إيرادات الفعاليات</option>
                                <option value="تبرعات">تبرعات</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>المصدر</th>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="revenuesTableBody">
                                <!-- Revenue data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Expenses Tab -->
                <div id="expenses-tab" class="tab-content">
                    <div class="tab-header">
                        <h3>المصروفات والنفقات</h3>
                        <div class="section-actions">
                            <button class="btn btn-danger" onclick="showModal('addExpenseModal')">
                                <i class="fas fa-minus"></i> إضافة مصروف جديد
                            </button>
                            <div class="search-input">
                                <input type="text" id="expenseSearch" placeholder="البحث في المصروفات..." onkeyup="searchExpenses()">
                                <i class="fas fa-search"></i>
                            </div>
                            <select id="expenseFilter" onchange="filterExpenses()">
                                <option value="">جميع المصروفات</option>
                                <option value="مصروفات المطعم">مصروفات المطعم</option>
                                <option value="مصروفات الرحلات">مصروفات الرحلات</option>
                                <option value="مصروفات الفعاليات">مصروفات الفعاليات</option>
                                <option value="إعانات صحية">إعانات صحية</option>
                                <option value="قروض وسلفيات">قروض وسلفيات</option>
                                <option value="مصروفات إدارية">مصروفات إدارية</option>
                                <option value="أخرى">أخرى</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>النوع</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>المستفيد</th>
                                    <th>الوصف</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="expensesTableBody">
                                <!-- Expense data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Loans Tab -->
                <div id="loans-tab" class="tab-content">
                    <div class="tab-header">
                        <h3>القروض والسلفيات</h3>
                        <div class="section-actions">
                            <button class="btn btn-primary" onclick="showModal('addLoanModal')">
                                <i class="fas fa-plus"></i> إضافة قرض جديد
                            </button>
                            <div class="search-input">
                                <input type="text" id="loanSearch" placeholder="البحث في القروض..." onkeyup="searchLoans()">
                                <i class="fas fa-search"></i>
                            </div>
                            <select id="loanFilter" onchange="filterLoans()">
                                <option value="">جميع القروض</option>
                                <option value="نشط">نشط</option>
                                <option value="مسدد">مسدد</option>
                                <option value="متأخر">متأخر</option>
                            </select>
                        </div>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>الموظف</th>
                                    <th>نوع القرض</th>
                                    <th>المبلغ الإجمالي</th>
                                    <th>المبلغ المسدد</th>
                                    <th>المبلغ المتبقي</th>
                                    <th>القسط الشهري</th>
                                    <th>تاريخ البداية</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="loansTableBody">
                                <!-- Loan data will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Treasury Tab -->
                <div id="treasury-tab" class="tab-content">
                    <div class="tab-header">
                        <h3>إدارة الخزينة</h3>
                        <div class="treasury-summary">
                            <div class="summary-item">
                                <label>الرصيد الافتتاحي:</label>
                                <span id="openingBalance">0 دج</span>
                            </div>
                            <div class="summary-item">
                                <label>إجمالي الإيرادات:</label>
                                <span id="treasuryTotalRevenue">0 دج</span>
                            </div>
                            <div class="summary-item">
                                <label>إجمالي المصروفات:</label>
                                <span id="treasuryTotalExpenses">0 دج</span>
                            </div>
                            <div class="summary-item">
                                <label>الرصيد الحالي:</label>
                                <span id="treasuryCurrentBalance" class="current-balance">0 دج</span>
                            </div>
                        </div>
                    </div>
                    <div class="treasury-transactions">
                        <h4>آخر المعاملات المالية</h4>
                        <div class="table-container">
                            <table class="data-table">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>الوصف</th>
                                        <th>الرصيد بعد المعاملة</th>
                                    </tr>
                                </thead>
                                <tbody id="treasuryTransactionsBody">
                                    <!-- Treasury transactions will be loaded here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </main>

    <!-- Add Revenue Modal -->
    <div id="addRevenueModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> إضافة إيراد جديد</h3>
                <span class="close" onclick="hideModal('addRevenueModal')">&times;</span>
            </div>
            <form id="addRevenueForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="revenueSource">مصدر الإيراد *</label>
                        <input type="text" id="revenueSource" name="source" required placeholder="مثال: اشتراكات شهر يناير">
                    </div>
                    <div class="form-group">
                        <label for="revenueType">نوع الإيراد *</label>
                        <select id="revenueType" name="type" required>
                            <option value="">اختر النوع</option>
                            <option value="اشتراكات الموظفين">اشتراكات الموظفين</option>
                            <option value="إيرادات المطعم">إيرادات المطعم</option>
                            <option value="إيرادات الرحلات">إيرادات الرحلات</option>
                            <option value="إيرادات الفعاليات">إيرادات الفعاليات</option>
                            <option value="تبرعات">تبرعات</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="revenueAmount">المبلغ (دج) *</label>
                        <input type="number" id="revenueAmount" name="amount" required min="0" step="100">
                    </div>
                    <div class="form-group">
                        <label for="revenueDate">تاريخ الإيراد *</label>
                        <input type="date" id="revenueDate" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="revenueStatus">الحالة *</label>
                        <select id="revenueStatus" name="status" required>
                            <option value="مؤكد">مؤكد</option>
                            <option value="معلق">معلق</option>
                            <option value="مستلم">مستلم</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="revenueCategory">الفئة</label>
                        <select id="revenueCategory" name="category">
                            <option value="">اختر الفئة</option>
                            <option value="شهري">شهري</option>
                            <option value="سنوي">سنوي</option>
                            <option value="موسمي">موسمي</option>
                            <option value="استثنائي">استثنائي</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="revenueDescription">وصف الإيراد *</label>
                        <textarea id="revenueDescription" name="description" rows="3" required placeholder="تفاصيل مصدر الإيراد..."></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label for="revenueNotes">ملاحظات إضافية</label>
                        <textarea id="revenueNotes" name="notes" rows="2" placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addRevenueModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveRevenue()">حفظ الإيراد</button>
            </div>
        </div>
    </div>

    <!-- Add Expense Modal -->
    <div id="addExpenseModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-minus"></i> إضافة مصروف جديد</h3>
                <span class="close" onclick="hideModal('addExpenseModal')">&times;</span>
            </div>
            <form id="addExpenseForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="expenseType">نوع المصروف *</label>
                        <select id="expenseType" name="type" required>
                            <option value="">اختر النوع</option>
                            <option value="مصروفات المطعم">مصروفات المطعم</option>
                            <option value="مصروفات الرحلات">مصروفات الرحلات</option>
                            <option value="مصروفات الفعاليات">مصروفات الفعاليات</option>
                            <option value="إعانات صحية">إعانات صحية</option>
                            <option value="قروض وسلفيات">قروض وسلفيات</option>
                            <option value="مصروفات إدارية">مصروفات إدارية</option>
                            <option value="أخرى">أخرى</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="expenseAmount">المبلغ (دج) *</label>
                        <input type="number" id="expenseAmount" name="amount" required min="0" step="100">
                    </div>
                    <div class="form-group">
                        <label for="expenseDate">تاريخ المصروف *</label>
                        <input type="date" id="expenseDate" name="date" required>
                    </div>
                    <div class="form-group">
                        <label for="expenseBeneficiary">المستفيد</label>
                        <input type="text" id="expenseBeneficiary" name="beneficiary" placeholder="اسم المستفيد أو الجهة">
                    </div>
                    <div class="form-group">
                        <label for="expenseStatus">الحالة *</label>
                        <select id="expenseStatus" name="status" required>
                            <option value="مدفوع">مدفوع</option>
                            <option value="معلق">معلق</option>
                            <option value="مؤجل">مؤجل</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="expenseCategory">الفئة</label>
                        <select id="expenseCategory" name="category">
                            <option value="">اختر الفئة</option>
                            <option value="ضروري">ضروري</option>
                            <option value="اختياري">اختياري</option>
                            <option value="طارئ">طارئ</option>
                            <option value="استثمار">استثمار</option>
                        </select>
                    </div>
                    <div class="form-group full-width">
                        <label for="expenseDescription">وصف المصروف *</label>
                        <textarea id="expenseDescription" name="description" rows="3" required placeholder="تفاصيل المصروف والغرض منه..."></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label for="expenseNotes">ملاحظات إضافية</label>
                        <textarea id="expenseNotes" name="notes" rows="2" placeholder="أي ملاحظات أو تفاصيل إضافية..."></textarea>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addExpenseModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveExpense()">حفظ المصروف</button>
            </div>
        </div>
    </div>



    <!-- Budget Planning Modal -->
    <div id="budgetPlanningModal" class="modal">
        <div class="modal-content" style="max-width: 1000px;">
            <div class="modal-header">
                <h3><i class="fas fa-calculator"></i> تخطيط الميزانية - السنة المالية <span id="budgetYear"></span></h3>
                <span class="close" onclick="hideModal('budgetPlanningModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Budget Overview -->
                <div class="budget-overview">
                    <h4><i class="fas fa-chart-line"></i> نظرة عامة على الميزانية</h4>
                    <div class="budget-comparison">
                        <div class="comparison-section">
                            <h5>الإيرادات</h5>
                            <div class="comparison-grid">
                                <div class="comparison-item">
                                    <label>المخطط:</label>
                                    <input type="number" id="plannedRevenue" min="0" step="1000" placeholder="الإيرادات المخططة">
                                </div>
                                <div class="comparison-item">
                                    <label>الفعلي:</label>
                                    <span id="actualRevenue">0 دج</span>
                                </div>
                                <div class="comparison-item">
                                    <label>الفرق:</label>
                                    <span id="revenueVariance">0 دج</span>
                                    <small id="revenueVariancePercent">0%</small>
                                </div>
                            </div>
                        </div>

                        <div class="comparison-section">
                            <h5>المصروفات</h5>
                            <div class="comparison-grid">
                                <div class="comparison-item">
                                    <label>المخطط:</label>
                                    <input type="number" id="plannedExpenses" min="0" step="1000" placeholder="المصروفات المخططة">
                                </div>
                                <div class="comparison-item">
                                    <label>الفعلي:</label>
                                    <span id="actualExpenses">0 دج</span>
                                </div>
                                <div class="comparison-item">
                                    <label>الفرق:</label>
                                    <span id="expenseVariance">0 دج</span>
                                    <small id="expenseVariancePercent">0%</small>
                                </div>
                            </div>
                        </div>

                        <div class="comparison-section">
                            <h5>صافي الميزانية</h5>
                            <div class="comparison-grid">
                                <div class="comparison-item">
                                    <label>المخطط:</label>
                                    <span id="plannedNet">0 دج</span>
                                </div>
                                <div class="comparison-item">
                                    <label>الفعلي:</label>
                                    <span id="actualNet">0 دج</span>
                                </div>
                                <div class="comparison-item">
                                    <label>الفرق:</label>
                                    <span id="netVariance">0 دج</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Category Budgets -->
                <div class="category-budgets">
                    <div class="budget-categories-section">
                        <h4><i class="fas fa-arrow-up"></i> ميزانيات فئات الإيرادات</h4>
                        <div id="revenueCategoriesBudget" class="categories-container">
                            <!-- Revenue category budgets will be loaded here -->
                        </div>
                    </div>

                    <div class="budget-categories-section">
                        <h4><i class="fas fa-arrow-down"></i> ميزانيات فئات المصروفات</h4>
                        <div id="expenseCategoriesBudget" class="categories-container">
                            <!-- Expense category budgets will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('budgetPlanningModal')">إلغاء</button>
                <button type="button" class="btn btn-info" onclick="exportBudgetReport()">
                    <i class="fas fa-download"></i> تصدير تقرير الميزانية
                </button>
                <button type="button" class="btn btn-primary" onclick="saveBudgetPlanning()">
                    <i class="fas fa-save"></i> حفظ الميزانية
                </button>
            </div>
        </div>
    </div>

    <!-- Add Loan Modal -->
    <div id="addLoanModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-hand-holding-usd"></i> إضافة قرض جديد</h3>
                <span class="close" onclick="hideModal('addLoanModal')">&times;</span>
            </div>
            <form id="addLoanForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="loanEmployee">الموظف *</label>
                        <select id="loanEmployee" name="employee" required>
                            <option value="">اختر الموظف</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="loanType">نوع القرض *</label>
                        <select id="loanType" name="type" required>
                            <option value="">اختر النوع</option>
                            <option value="قرض شخصي">قرض شخصي</option>
                            <option value="سلفة راتب">سلفة راتب</option>
                            <option value="قرض سكن">قرض سكن</option>
                            <option value="قرض تعليمي">قرض تعليمي</option>
                            <option value="قرض طارئ">قرض طارئ</option>
                            <option value="قرض زواج">قرض زواج</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="loanAmount">المبلغ الإجمالي (دج) *</label>
                        <input type="number" id="loanAmount" name="amount" required min="1000" step="1000">
                    </div>
                    <div class="form-group">
                        <label for="loanInstallments">عدد الأقساط *</label>
                        <input type="number" id="loanInstallments" name="installments" required min="1" max="60" onchange="calculateMonthlyInstallment()">
                    </div>
                    <div class="form-group">
                        <label for="loanStartDate">تاريخ البداية *</label>
                        <input type="date" id="loanStartDate" name="startDate" required>
                    </div>
                    <div class="form-group">
                        <label for="monthlyInstallment">القسط الشهري (دج)</label>
                        <input type="number" id="monthlyInstallment" name="monthlyInstallment" readonly>
                    </div>
                    <div class="form-group full-width">
                        <label for="loanPurpose">الغرض من القرض *</label>
                        <textarea id="loanPurpose" name="purpose" rows="3" required placeholder="اذكر الغرض من القرض بالتفصيل..."></textarea>
                    </div>
                    <div class="form-group full-width">
                        <label for="loanNotes">ملاحظات إضافية</label>
                        <textarea id="loanNotes" name="notes" rows="2" placeholder="أي ملاحظات أو شروط إضافية..."></textarea>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addLoanModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveLoan()">حفظ القرض</button>
            </div>
        </div>
    </div>

    <!-- Pay Installment Modal -->
    <div id="payInstallmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill"></i> دفع قسط القرض</h3>
                <span class="close" onclick="hideModal('payInstallmentModal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="paymentLoanId">

                <!-- Loan Information -->
                <div class="loan-info-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات القرض</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>اسم الموظف:</label>
                            <span id="paymentEmployeeName">-</span>
                        </div>
                        <div class="info-item">
                            <label>نوع القرض:</label>
                            <span id="paymentLoanType">-</span>
                        </div>
                        <div class="info-item">
                            <label>المبلغ الإجمالي:</label>
                            <span id="paymentTotalAmount">-</span>
                        </div>
                        <div class="info-item">
                            <label>المبلغ المسدد:</label>
                            <span id="paymentPaidAmount">-</span>
                        </div>
                        <div class="info-item">
                            <label>المبلغ المتبقي:</label>
                            <span id="paymentRemainingAmount" class="remaining-amount">-</span>
                        </div>
                        <div class="info-item">
                            <label>القسط الشهري:</label>
                            <span id="paymentMonthlyInstallment">-</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <form id="payInstallmentForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="paymentInstallmentAmount">مبلغ القسط (دج) *</label>
                            <input type="number" id="paymentInstallmentAmount" name="amount" required min="0" step="100">
                        </div>
                        <div class="form-group">
                            <label for="paymentDate">تاريخ الدفع *</label>
                            <input type="date" id="paymentDate" name="date" required>
                        </div>
                        <div class="form-group full-width">
                            <label for="paymentNotes">ملاحظات</label>
                            <textarea id="paymentNotes" name="notes" rows="3" placeholder="ملاحظات حول الدفع..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('payInstallmentModal')">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveInstallmentPayment()">
                    <i class="fas fa-check"></i> تأكيد الدفع
                </button>
            </div>
        </div>
    </div>

    <!-- Expense Breakdown Modal -->
    <div id="expenseBreakdownModal" class="modal">
        <div class="modal-content" style="max-width: 1200px;">
            <div class="modal-header">
                <h3><i class="fas fa-chart-bar"></i> تفصيل المصروفات - السنة المالية <span id="expenseBreakdownYear"></span></h3>
                <span class="close" onclick="hideModal('expenseBreakdownModal')">&times;</span>
            </div>
            <div class="modal-body">
                <div id="expenseBreakdownContent">
                    <!-- Expense breakdown content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('expenseBreakdownModal')">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="exportExpenseBreakdownReport()">
                    <i class="fas fa-download"></i> تصدير تقرير التفصيل
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 نظام إدارة لجنة الخدمات الاجتماعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="advanced-database.js"></script>
    <script src="database-adapter.js"></script>
    <script src="script.js"></script>
    <script src="financial.js"></script>
</body>
</html>
