// Loans Management System

// Initialize loans data when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeLoansData();
    loadEmployeeOptions();
    setupEventListeners();
    displayLoansData();
    updateLoansStatistics();
    setCurrentDate();
});

// Initialize loans data
function initializeLoansData() {
    // Initialize loans if not exists
    if (!localStorage.getItem('loans')) {
        localStorage.setItem('loans', JSON.stringify([]));
    }

    // Initialize loan payments if not exists
    if (!localStorage.getItem('loanPayments')) {
        localStorage.setItem('loanPayments', JSON.stringify([]));
    }
}

// Load employee options for dropdowns
function loadEmployeeOptions() {
    const employees = db.getEmployees();
    const loanBeneficiarySelect = document.getElementById('loanBeneficiary');

    if (loanBeneficiarySelect) {
        loanBeneficiarySelect.innerHTML = '<option value="">اختر الموظف</option>';
        employees.forEach(employee => {
            const option = document.createElement('option');
            option.value = employee.id;
            option.textContent = `${employee.name} - ${employee.department}`;
            loanBeneficiarySelect.appendChild(option);
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Loan calculation listeners
    const loanAmountInput = document.getElementById('loanAmount');
    const loanInstallmentsInput = document.getElementById('loanInstallments');

    if (loanAmountInput && loanInstallmentsInput) {
        loanAmountInput.addEventListener('input', calculateLoanDetails);
        loanInstallmentsInput.addEventListener('input', calculateLoanDetails);
    }

    // Set default date to today
    const loanStartDateInput = document.getElementById('loanStartDate');
    if (loanStartDateInput) {
        loanStartDateInput.value = new Date().toISOString().split('T')[0];
    }
}

// Calculate loan details (without interest - Sharia compliant)
function calculateLoanDetails() {
    const amount = parseFloat(document.getElementById('loanAmount').value) || 0;
    const installments = parseInt(document.getElementById('loanInstallments').value) || 1;

    if (amount > 0 && installments > 0) {
        // Simple division without interest (Sharia compliant)
        const monthlyPayment = amount / installments;

        // Display calculated values
        document.getElementById('monthlyPaymentDisplay').textContent = `${Math.round(monthlyPayment).toLocaleString()} دج`;
        document.getElementById('totalAmountDisplay').textContent = `${amount.toLocaleString()} دج`;
    } else {
        document.getElementById('monthlyPaymentDisplay').textContent = '0 دج';
        document.getElementById('totalAmountDisplay').textContent = '0 دج';
    }
}

// Set current date
function setCurrentDate() {
    const currentDateElement = document.getElementById('currentDate');
    if (currentDateElement) {
        const today = new Date();
        const options = {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        };
        currentDateElement.textContent = today.toLocaleDateString('ar-DZ', options);
    }
}

// Save loan (Sharia compliant - no interest)
function saveLoan() {
    const beneficiaryId = document.getElementById('loanBeneficiary').value;
    const type = document.getElementById('loanType').value;
    const amount = parseFloat(document.getElementById('loanAmount').value);
    const installments = parseInt(document.getElementById('loanInstallments').value);
    const startDate = document.getElementById('loanStartDate').value;
    const purpose = document.getElementById('loanPurpose').value.trim();

    // Validation
    if (!beneficiaryId || !type || !amount || !installments || !startDate || !purpose) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (installments > 60) {
        showAlert('عدد الأقساط لا يمكن أن يزيد عن 60 قسط', 'error');
        return;
    }

    if (amount < 1000) {
        showAlert('مبلغ القرض يجب أن يكون 1000 دج على الأقل', 'error');
        return;
    }

    // Get employee info
    const employee = db.getEmployees().find(emp => emp.id == beneficiaryId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Calculate monthly payment (simple division - no interest)
    const monthlyPayment = amount / installments;

    // Create loan object
    const loan = {
        id: Date.now(),
        beneficiaryId: parseInt(beneficiaryId),
        beneficiaryName: employee.name,
        beneficiaryDepartment: employee.department,
        type,
        amount,
        installments,
        monthlyPayment: Math.round(monthlyPayment),
        startDate,
        purpose,
        paidAmount: 0,
        remainingAmount: amount,
        status: 'نشط',
        createdAt: new Date().toISOString()
    };

    // Save loan
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');
    loans.push(loan);
    localStorage.setItem('loans', JSON.stringify(loans));

    showAlert('تم حفظ القرض بنجاح (بدون فوائد - متوافق مع الأحكام الشرعية)', 'success');
    hideModal('addLoanModal');
    resetLoanForm();
    displayLoansData();
    updateLoansStatistics();
}

// Reset loan form
function resetLoanForm() {
    document.getElementById('addLoanForm').reset();
    document.getElementById('loanStartDate').value = new Date().toISOString().split('T')[0];
    document.getElementById('monthlyPaymentDisplay').textContent = '0 دج';
    document.getElementById('totalAmountDisplay').textContent = '0 دج';
}

// Display loans data
function displayLoansData() {
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');
    const tableBody = document.getElementById('loansTableBody');

    if (!tableBody) return;

    if (loans.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                        <h3>لا توجد قروض مسجلة</h3>
                        <p>اضغط على "إضافة قرض جديد" لبدء تسجيل القروض</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = loans.map(loan => {
        const remainingAmount = loan.amount - (loan.paidAmount || 0);
        const status = remainingAmount <= 0 ? 'مسدد' : 'نشط';

        return `
            <tr>
                <td><strong>#${loan.id}</strong></td>
                <td>
                    <div class="employee-info">
                        <strong>${loan.beneficiaryName}</strong>
                        <small>${loan.beneficiaryDepartment}</small>
                    </div>
                </td>
                <td>
                    <span class="loan-type-badge">${loan.type}</span>
                </td>
                <td><strong>${loan.amount.toLocaleString()} دج</strong></td>
                <td class="paid-amount">${(loan.paidAmount || 0).toLocaleString()} دج</td>
                <td class="remaining-amount">${remainingAmount.toLocaleString()} دج</td>
                <td>${loan.monthlyPayment.toLocaleString()} دج</td>
                <td>
                    <span class="status-badge ${status === 'مسدد' ? 'active' : 'inactive'}">
                        ${status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewLoanDetails(${loan.id})" title="عرض التفاصيل">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="btn-icon btn-success" onclick="payInstallment(${loan.id})" title="دفع قسط" ${status === 'مسدد' ? 'disabled' : ''}>
                            <i class="fas fa-money-bill"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteLoan(${loan.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Update loans statistics
function updateLoansStatistics() {
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');

    const totalLoansCount = loans.length;
    const totalLoansAmount = loans.reduce((sum, loan) => sum + loan.amount, 0);
    const totalPaidAmount = loans.reduce((sum, loan) => sum + (loan.paidAmount || 0), 0);
    const totalRemainingAmount = totalLoansAmount - totalPaidAmount;

    // Update DOM elements
    const totalLoansCountEl = document.getElementById('totalLoansCount');
    const totalLoansAmountEl = document.getElementById('totalLoansAmount');
    const totalPaidAmountEl = document.getElementById('totalPaidAmount');
    const totalRemainingAmountEl = document.getElementById('totalRemainingAmount');

    if (totalLoansCountEl) totalLoansCountEl.textContent = totalLoansCount;
    if (totalLoansAmountEl) totalLoansAmountEl.textContent = `${totalLoansAmount.toLocaleString()} دج`;
    if (totalPaidAmountEl) totalPaidAmountEl.textContent = `${totalPaidAmount.toLocaleString()} دج`;
    if (totalRemainingAmountEl) totalRemainingAmountEl.textContent = `${totalRemainingAmount.toLocaleString()} دج`;
}

// Search loans
function searchLoans() {
    const searchTerm = document.getElementById('loansSearch').value.toLowerCase();
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');

    const filteredLoans = loans.filter(loan =>
        loan.beneficiaryName.toLowerCase().includes(searchTerm) ||
        loan.type.toLowerCase().includes(searchTerm) ||
        loan.purpose.toLowerCase().includes(searchTerm)
    );

    displayFilteredLoans(filteredLoans);
}

// Filter loans
function filterLoans() {
    const filterValue = document.getElementById('loansFilter').value;
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');

    let filteredLoans = loans;

    if (filterValue) {
        if (filterValue === 'نشط') {
            filteredLoans = loans.filter(loan => (loan.amount - (loan.paidAmount || 0)) > 0);
        } else if (filterValue === 'مسدد') {
            filteredLoans = loans.filter(loan => (loan.amount - (loan.paidAmount || 0)) <= 0);
        } else {
            filteredLoans = loans.filter(loan => loan.type === filterValue);
        }
    }

    displayFilteredLoans(filteredLoans);
}

// Display filtered loans
function displayFilteredLoans(loans) {
    const tableBody = document.getElementById('loansTableBody');

    if (!tableBody) return;

    if (loans.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على قروض تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = loans.map(loan => {
        const remainingAmount = loan.amount - (loan.paidAmount || 0);
        const status = remainingAmount <= 0 ? 'مسدد' : 'نشط';

        return `
            <tr>
                <td><strong>#${loan.id}</strong></td>
                <td>
                    <div class="employee-info">
                        <strong>${loan.beneficiaryName}</strong>
                        <small>${loan.beneficiaryDepartment}</small>
                    </div>
                </td>
                <td>
                    <span class="loan-type-badge">${loan.type}</span>
                </td>
                <td><strong>${loan.amount.toLocaleString()} دج</strong></td>
                <td class="paid-amount">${(loan.paidAmount || 0).toLocaleString()} دج</td>
                <td class="remaining-amount">${remainingAmount.toLocaleString()} دج</td>
                <td>${loan.monthlyPayment.toLocaleString()} دج</td>
                <td>
                    <span class="status-badge ${status === 'مسدد' ? 'active' : 'inactive'}">
                        ${status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewLoanDetails(${loan.id})" title="عرض التفاصيل">
                            <i class="fas fa-info-circle"></i>
                        </button>
                        <button class="btn-icon btn-success" onclick="payInstallment(${loan.id})" title="دفع قسط" ${status === 'مسدد' ? 'disabled' : ''}>
                            <i class="fas fa-money-bill"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteLoan(${loan.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
}

// Show active loans
function showActiveLoans() {
    document.getElementById('loansFilter').value = 'نشط';
    filterLoans();
    showAlert('تم عرض القروض النشطة فقط', 'info');
}

// Show paid loans
function showPaidLoans() {
    document.getElementById('loansFilter').value = 'مسدد';
    filterLoans();
    showAlert('تم عرض القروض المسددة فقط', 'info');
}

// Show overdue payments
function showOverduePayments() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Show payment schedule
function showPaymentSchedule() {
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');
    const activeLoans = loans.filter(loan => (loan.amount - (loan.paidAmount || 0)) > 0);

    if (activeLoans.length === 0) {
        showAlert('لا توجد قروض نشطة لعرض جدولة الدفعات', 'warning');
        return;
    }

    const scheduleContainer = document.getElementById('scheduleContainer');
    const paymentScheduleSection = document.getElementById('paymentScheduleSection');

    if (!scheduleContainer || !paymentScheduleSection) return;

    // Generate payment schedule
    let scheduleHTML = '<div class="schedule-grid">';

    activeLoans.forEach(loan => {
        const remainingAmount = loan.amount - (loan.paidAmount || 0);
        const remainingInstallments = Math.ceil(remainingAmount / loan.monthlyPayment);

        scheduleHTML += `
            <div class="schedule-card">
                <div class="schedule-header">
                    <h4>${loan.beneficiaryName}</h4>
                    <span class="loan-type-badge">${loan.type}</span>
                </div>
                <div class="schedule-details">
                    <div class="schedule-item">
                        <label>المبلغ المتبقي:</label>
                        <span class="remaining-amount">${remainingAmount.toLocaleString()} دج</span>
                    </div>
                    <div class="schedule-item">
                        <label>القسط الشهري:</label>
                        <span>${loan.monthlyPayment.toLocaleString()} دج</span>
                    </div>
                    <div class="schedule-item">
                        <label>الأقساط المتبقية:</label>
                        <span>${remainingInstallments} قسط</span>
                    </div>
                </div>
                <div class="schedule-actions">
                    <button class="btn btn-success btn-sm" onclick="payInstallment(${loan.id})">
                        <i class="fas fa-money-bill"></i> دفع قسط
                    </button>
                </div>
            </div>
        `;
    });

    scheduleHTML += '</div>';
    scheduleContainer.innerHTML = scheduleHTML;
    paymentScheduleSection.style.display = 'block';
}

// Hide payment schedule
function hidePaymentSchedule() {
    const paymentScheduleSection = document.getElementById('paymentScheduleSection');
    if (paymentScheduleSection) {
        paymentScheduleSection.style.display = 'none';
    }
}

// Pay installment
function payInstallment(loanId) {
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');
    const loan = loans.find(l => l.id === loanId);

    if (!loan) {
        showAlert('لم يتم العثور على القرض', 'error');
        return;
    }

    const remainingAmount = loan.amount - (loan.paidAmount || 0);

    if (remainingAmount <= 0) {
        showAlert('هذا القرض مسدد بالكامل', 'info');
        return;
    }

    // Fill payment modal
    document.getElementById('paymentLoanId').value = loanId;
    document.getElementById('paymentEmployeeName').textContent = loan.beneficiaryName;
    document.getElementById('paymentLoanType').textContent = loan.type;
    document.getElementById('paymentTotalAmount').textContent = `${loan.amount.toLocaleString()} دج`;
    document.getElementById('paymentPaidAmount').textContent = `${(loan.paidAmount || 0).toLocaleString()} دج`;
    document.getElementById('paymentRemainingAmount').textContent = `${remainingAmount.toLocaleString()} دج`;
    document.getElementById('paymentMonthlyAmount').textContent = `${loan.monthlyPayment.toLocaleString()} دج`;
    document.getElementById('paymentInstallmentAmount').value = Math.min(loan.monthlyPayment, remainingAmount);
    document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];

    showModal('payInstallmentModal');
}

// Save installment payment
function saveInstallmentPayment() {
    const loanId = parseInt(document.getElementById('paymentLoanId').value);
    const paymentAmount = parseFloat(document.getElementById('paymentInstallmentAmount').value);
    const paymentDate = document.getElementById('paymentDate').value;
    const paymentNotes = document.getElementById('paymentNotes').value.trim();

    if (!paymentAmount || paymentAmount <= 0) {
        showAlert('يرجى إدخال مبلغ صحيح', 'error');
        return;
    }

    if (!paymentDate) {
        showAlert('يرجى تحديد تاريخ الدفع', 'error');
        return;
    }

    // Find and update loan
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');
    const loanIndex = loans.findIndex(loan => loan.id === loanId);

    if (loanIndex === -1) {
        showAlert('لم يتم العثور على القرض', 'error');
        return;
    }

    const loan = loans[loanIndex];
    const currentPaid = loan.paidAmount || 0;
    const remainingAmount = loan.amount - currentPaid;

    if (paymentAmount > remainingAmount) {
        showAlert('مبلغ الدفع أكبر من المبلغ المتبقي', 'error');
        return;
    }

    // Update loan
    loan.paidAmount = currentPaid + paymentAmount;

    // Update status if fully paid
    if (loan.paidAmount >= loan.amount) {
        loan.status = 'مسدد';
    }

    // Save payment record
    const payment = {
        id: Date.now(),
        loanId: loanId,
        amount: paymentAmount,
        date: paymentDate,
        notes: paymentNotes,
        createdAt: new Date().toISOString()
    };

    // Save data
    localStorage.setItem('loans', JSON.stringify(loans));

    const payments = JSON.parse(localStorage.getItem('loanPayments') || '[]');
    payments.push(payment);
    localStorage.setItem('loanPayments', JSON.stringify(payments));

    showAlert('تم تسجيل دفع القسط بنجاح', 'success');
    hideModal('payInstallmentModal');
    resetPaymentForm();
    displayLoansData();
    updateLoansStatistics();
}

// Reset payment form
function resetPaymentForm() {
    document.getElementById('payInstallmentForm').reset();
    document.getElementById('paymentDate').value = new Date().toISOString().split('T')[0];
}

// View loan details
function viewLoanDetails(loanId) {
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');
    const loan = loans.find(l => l.id === loanId);

    if (!loan) {
        showAlert('لم يتم العثور على القرض', 'error');
        return;
    }

    const payments = JSON.parse(localStorage.getItem('loanPayments') || '[]')
        .filter(payment => payment.loanId === loanId);

    // Fill details modal
    document.getElementById('detailsEmployeeName').textContent = loan.beneficiaryName;
    document.getElementById('detailsLoanType').textContent = loan.type;
    document.getElementById('detailsTotalAmount').textContent = `${loan.amount.toLocaleString()} دج`;
    document.getElementById('detailsPaidAmount').textContent = `${(loan.paidAmount || 0).toLocaleString()} دج`;
    document.getElementById('detailsRemainingAmount').textContent = `${(loan.amount - (loan.paidAmount || 0)).toLocaleString()} دج`;
    document.getElementById('detailsMonthlyInstallment').textContent = `${loan.monthlyPayment.toLocaleString()} دج`;
    document.getElementById('detailsInstallments').textContent = loan.installments;
    document.getElementById('detailsStartDate').textContent = formatDate(loan.startDate);
    document.getElementById('detailsStatus').textContent = loan.status || 'نشط';
    document.getElementById('detailsPurpose').textContent = loan.purpose || 'غير محدد';

    // Display payment history
    const paymentsTableBody = document.getElementById('paymentsTableBody');
    if (paymentsTableBody) {
        if (payments.length === 0) {
            paymentsTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-money-bill fa-2x"></i>
                            <p>لا توجد دفعات مسجلة</p>
                        </div>
                    </td>
                </tr>
            `;
        } else {
            paymentsTableBody.innerHTML = payments.map((payment, index) => `
                <tr>
                    <td>${index + 1}</td>
                    <td><strong>${payment.amount.toLocaleString()} دج</strong></td>
                    <td>${formatDate(payment.date)}</td>
                    <td>${payment.notes || '-'}</td>
                </tr>
            `).join('');
        }
    }

    showModal('loanDetailsModal');
}

// Delete loan
function deleteLoan(loanId) {
    if (confirm('هل أنت متأكد من حذف هذا القرض؟ سيتم حذف جميع الدفعات المرتبطة به أيضاً.')) {
        // Delete loan
        const loans = JSON.parse(localStorage.getItem('loans') || '[]');
        const updatedLoans = loans.filter(loan => loan.id !== loanId);
        localStorage.setItem('loans', JSON.stringify(updatedLoans));

        // Delete related payments
        const payments = JSON.parse(localStorage.getItem('loanPayments') || '[]');
        const updatedPayments = payments.filter(payment => payment.loanId !== loanId);
        localStorage.setItem('loanPayments', JSON.stringify(updatedPayments));

        showAlert('تم حذف القرض بنجاح', 'success');
        displayLoansData();
        updateLoansStatistics();
    }
}

// Export loans report
function exportLoansReport() {
    const loans = JSON.parse(localStorage.getItem('loans') || '[]');

    if (loans.length === 0) {
        showAlert('لا توجد قروض لتصديرها', 'warning');
        return;
    }

    const totalAmount = loans.reduce((sum, loan) => sum + loan.amount, 0);
    const totalPaid = loans.reduce((sum, loan) => sum + (loan.paidAmount || 0), 0);
    const totalRemaining = totalAmount - totalPaid;

    let reportContent = `تقرير القروض والسلفيات (بدون فوائد - متوافق مع الأحكام الشرعية)
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

إحصائيات عامة:
- عدد القروض: ${loans.length}
- إجمالي المبالغ المقترضة: ${totalAmount.toLocaleString()} دج
- إجمالي المبالغ المسددة: ${totalPaid.toLocaleString()} دج
- إجمالي المبالغ المتبقية: ${totalRemaining.toLocaleString()} دج

ملاحظة: جميع القروض بدون فوائد وفقاً للأحكام الشرعية

تفاصيل القروض:
`;

    loans.forEach((loan, index) => {
        const remainingAmount = loan.amount - (loan.paidAmount || 0);

        reportContent += `
${index + 1}. ${loan.beneficiaryName}
   نوع القرض: ${loan.type}
   المبلغ الإجمالي: ${loan.amount.toLocaleString()} دج
   المبلغ المسدد: ${(loan.paidAmount || 0).toLocaleString()} دج
   المبلغ المتبقي: ${remainingAmount.toLocaleString()} دج
   القسط الشهري: ${loan.monthlyPayment.toLocaleString()} دج
   عدد الأقساط: ${loan.installments}
   تاريخ البداية: ${formatDate(loan.startDate)}
   الحالة: ${loan.status || 'نشط'}
   الغرض: ${loan.purpose}
`;
    });

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_القروض_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert('تم تصدير تقرير القروض بنجاح', 'success');
}

// Export loan details
function exportLoanDetails() {
    showAlert('سيتم تطوير هذه الميزة قريباً', 'info');
}

// Format date for display
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}