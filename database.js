// محاكاة قاعدة البيانات المحلية
class LocalDatabase {
    constructor() {
        this.initializeDatabase();
    }

    // تهيئة قاعدة البيانات
    initializeDatabase() {
        // إنشاء الجداول إذا لم تكن موجودة
        if (!localStorage.getItem('employees')) {
            localStorage.setItem('employees', JSON.stringify([]));
        }
        if (!localStorage.getItem('financial_aids')) {
            localStorage.setItem('financial_aids', JSON.stringify([]));
        }
        if (!localStorage.getItem('equipment_requests')) {
            localStorage.setItem('equipment_requests', JSON.stringify([]));
        }
        if (!localStorage.getItem('trips')) {
            localStorage.setItem('trips', JSON.stringify([]));
        }
        if (!localStorage.getItem('events')) {
            localStorage.setItem('events', JSON.stringify([]));
        }
        if (!localStorage.getItem('health_aids')) {
            localStorage.setItem('health_aids', JSON.stringify([]));
        }
        if (!localStorage.getItem('activities')) {
            localStorage.setItem('activities', JSON.stringify([]));
        }
        if (!localStorage.getItem('workplaces')) {
            localStorage.setItem('workplaces', JSON.stringify([
                'المديرية العامة',
                'مديرية الموارد البشرية',
                'مديرية المالية والمحاسبة',
                'مديرية التخطيط والتطوير',
                'مديرية الشؤون القانونية',
                'مديرية التكنولوجيا والمعلومات',
                'مديرية الخدمات العامة'
            ]));
        }
        if (!localStorage.getItem('bank_branches')) {
            localStorage.setItem('bank_branches', JSON.stringify([
                'البنك الوطني الجزائري - الوكالة المركزية',
                'البنك الوطني الجزائري - وكالة الجزائر',
                'البنك الوطني الجزائري - وكالة وهران',
                'البنك الوطني الجزائري - وكالة قسنطينة',
                'بنك الفلاحة والتنمية الريفية - الوكالة المركزية',
                'بنك الفلاحة والتنمية الريفية - وكالة الجزائر',
                'القرض الشعبي الجزائري - الوكالة المركزية',
                'القرض الشعبي الجزائري - وكالة الجزائر',
                'بنك التنمية المحلية - الوكالة المركزية',
                'بنك التنمية المحلية - وكالة الجزائر',
                'بريد الجزائر - المركز الرئيسي',
                'بريد الجزائر - وكالة الجزائر المركز',
                'بريد الجزائر - وكالة وهران',
                'بريد الجزائر - وكالة قسنطينة'
            ]));
        }
        if (!localStorage.getItem('settings')) {
            localStorage.setItem('settings', JSON.stringify({
                currency: 'DZD',
                dateFormat: 'DD/MM/YYYY',
                language: 'ar'
            }));
        }

        // إضافة بيانات تجريبية
        this.seedDatabase();
    }

    // إضافة بيانات تجريبية
    seedDatabase() {
        const employees = this.getEmployees();
        if (employees.length === 0) {
            const sampleEmployees = [
                {
                    id: 1,
                    name: 'أحمد محمد علي',
                    position: 'مهندس',
                    department: 'مديرية التكنولوجيا والمعلومات',
                    gender: 'ذكر',
                    birthDate: '1985-05-15',
                    hireDate: '2010-03-01',
                    salary: 85000,
                    bankAccount: '********************',
                    bankBranch: 'البنك الوطني الجزائري - وكالة الجزائر',
                    children: [
                        { name: 'محمد أحمد', birthDate: '2010-08-20', educationLevel: 'ابتدائي' },
                        { name: 'فاطمة أحمد', birthDate: '2012-12-10', educationLevel: 'ابتدائي' }
                    ],
                    photo: null,
                    phone: '**********',
                    address: 'الجزائر العاصمة',
                    status: 'نشط'
                },
                {
                    id: 2,
                    name: 'فاطمة الزهراء بن علي',
                    position: 'محاسبة',
                    department: 'مديرية المالية والمحاسبة',
                    gender: 'أنثى',
                    birthDate: '1990-03-22',
                    hireDate: '2015-09-01',
                    salary: 75000,
                    bankAccount: '**********',
                    bankBranch: 'بريد الجزائر - وكالة الجزائر المركز',
                    children: [
                        { name: 'عائشة فاطمة', birthDate: '2015-06-15', educationLevel: 'ابتدائي' }
                    ],
                    photo: null,
                    phone: '**********',
                    address: 'وهران',
                    status: 'نشط'
                }
            ];
            localStorage.setItem('employees', JSON.stringify(sampleEmployees));
        }

        const financialAids = this.getFinancialAids();
        if (financialAids.length === 0) {
            const sampleFinancialAids = [
                {
                    id: 1,
                    category: 'aid',
                    beneficiaryId: 1,
                    type: 'إعانة طارئة',
                    amount: 50000,
                    date: '2025-01-15',
                    description: 'إعانة طارئة للظروف الاستثنائية',
                    status: 'مصروف',
                    createdAt: new Date().toISOString()
                },
                {
                    id: 2,
                    category: 'grant',
                    beneficiaryId: 2,
                    type: 'منحة عيد الأضحى',
                    amount: 30000,
                    date: '2024-12-25',
                    occasion: 'عيد الأضحى المبارك',
                    notes: 'منحة سنوية بمناسبة عيد الأضحى',
                    status: 'مصروف',
                    createdAt: new Date(Date.now() - 86400000 * 30).toISOString()
                },
                {
                    id: 3,
                    category: 'loan',
                    beneficiaryId: 1,
                    type: 'قرض شخصي',
                    amount: 200000,
                    installments: 12,
                    interestRate: 0,
                    monthlyInstallment: 16667,
                    paidAmount: 50000,
                    startDate: '2024-12-01',
                    purpose: 'قرض شخصي للظروف العائلية',
                    status: 'نشط',
                    createdAt: new Date(Date.now() - 86400000 * 45).toISOString()
                }
            ];
            localStorage.setItem('financial_aids', JSON.stringify(sampleFinancialAids));
        }

        const activities = this.getActivities();
        if (activities.length === 0) {
            const sampleActivities = [
                {
                    id: 1,
                    type: 'employee',
                    title: 'إضافة موظف جديد',
                    description: 'تم إضافة الموظف أحمد محمد علي',
                    date: new Date().toISOString(),
                    icon: 'user-plus',
                    color: '#3498db'
                },
                {
                    id: 2,
                    type: 'financial',
                    title: 'إعانة مالية',
                    description: 'تم صرف إعانة طارئة بمبلغ 50,000 دج',
                    date: new Date(Date.now() - 86400000).toISOString(),
                    icon: 'hand-holding-usd',
                    color: '#2ecc71'
                },
                {
                    id: 3,
                    type: 'financial',
                    title: 'منحة عيد الأضحى',
                    description: 'تم صرف منحة عيد الأضحى بمبلغ 30,000 دج',
                    date: new Date(Date.now() - 86400000 * 30).toISOString(),
                    icon: 'gift',
                    color: '#3498db'
                },
                {
                    id: 4,
                    type: 'financial',
                    title: 'قرض شخصي',
                    description: 'تم منح قرض شخصي بمبلغ 200,000 دج',
                    date: new Date(Date.now() - 86400000 * 45).toISOString(),
                    icon: 'credit-card',
                    color: '#f39c12'
                }
            ];
            localStorage.setItem('activities', JSON.stringify(sampleActivities));
        }
    }

    // وظائف الموظفين
    getEmployees() {
        return JSON.parse(localStorage.getItem('employees') || '[]');
    }

    addEmployee(employee) {
        const employees = this.getEmployees();
        employee.id = Date.now();
        employee.createdAt = new Date().toISOString();
        employees.push(employee);
        localStorage.setItem('employees', JSON.stringify(employees));

        // إضافة نشاط
        this.addActivity({
            type: 'employee',
            title: 'إضافة موظف جديد',
            description: `تم إضافة الموظف ${employee.name}`,
            icon: 'user-plus',
            color: '#3498db'
        });

        return employee;
    }

    updateEmployee(id, updatedEmployee) {
        const employees = this.getEmployees();
        const index = employees.findIndex(emp => emp.id === id);
        if (index !== -1) {
            employees[index] = { ...employees[index], ...updatedEmployee };
            localStorage.setItem('employees', JSON.stringify(employees));

            this.addActivity({
                type: 'employee',
                title: 'تحديث بيانات موظف',
                description: `تم تحديث بيانات الموظف ${employees[index].name}`,
                icon: 'user-edit',
                color: '#f39c12'
            });

            return employees[index];
        }
        return null;
    }

    deleteEmployee(id) {
        const employees = this.getEmployees();
        const index = employees.findIndex(emp => emp.id === id);
        if (index !== -1) {
            const deletedEmployee = employees[index];
            employees.splice(index, 1);
            localStorage.setItem('employees', JSON.stringify(employees));

            this.addActivity({
                type: 'employee',
                title: 'حذف موظف',
                description: `تم حذف الموظف ${deletedEmployee.name}`,
                icon: 'user-minus',
                color: '#e74c3c'
            });

            return deletedEmployee;
        }
        return null;
    }

    // وظائف الإعانات المالية
    getFinancialAids() {
        return JSON.parse(localStorage.getItem('financial_aids') || '[]');
    }

    addFinancialAid(aid) {
        const aids = this.getFinancialAids();
        aid.id = Date.now();
        aid.createdAt = new Date().toISOString();
        aids.push(aid);
        localStorage.setItem('financial_aids', JSON.stringify(aids));

        this.addActivity({
            type: 'financial',
            title: 'إعانة مالية جديدة',
            description: `تم إضافة إعانة ${aid.type} بمبلغ ${aid.amount} دج`,
            icon: 'hand-holding-usd',
            color: '#2ecc71'
        });

        return aid;
    }

    // وظائف طلبات التجهيزات
    getEquipmentRequests() {
        return JSON.parse(localStorage.getItem('equipment_requests') || '[]');
    }

    addEquipmentRequest(request) {
        const requests = this.getEquipmentRequests();
        request.id = Date.now();
        request.createdAt = new Date().toISOString();
        request.status = 'قيد المراجعة';
        requests.push(request);
        localStorage.setItem('equipment_requests', JSON.stringify(requests));

        this.addActivity({
            type: 'equipment',
            title: 'طلب تجهيزات جديد',
            description: `تم تسجيل طلب ${request.equipmentType}`,
            icon: 'shopping-cart',
            color: '#9b59b6'
        });

        return request;
    }

    // وظائف الرحلات
    getTrips() {
        return JSON.parse(localStorage.getItem('trips') || '[]');
    }

    addTrip(trip) {
        const trips = this.getTrips();
        trip.id = Date.now();
        trip.createdAt = new Date().toISOString();
        trips.push(trip);
        localStorage.setItem('trips', JSON.stringify(trips));

        this.addActivity({
            type: 'trip',
            title: 'رحلة جديدة',
            description: `تم تنظيم رحلة إلى ${trip.destination}`,
            icon: 'plane',
            color: '#e67e22'
        });

        return trip;
    }

    // وظائف الفعاليات
    getEvents() {
        return JSON.parse(localStorage.getItem('events') || '[]');
    }

    addEvent(event) {
        const events = this.getEvents();
        event.id = Date.now();
        event.createdAt = new Date().toISOString();
        events.push(event);
        localStorage.setItem('events', JSON.stringify(events));

        this.addActivity({
            type: 'event',
            title: 'فعالية جديدة',
            description: `تم تنظيم فعالية ${event.title}`,
            icon: 'calendar-alt',
            color: '#1abc9c'
        });

        return event;
    }

    // وظائف الإعانات الصحية
    getHealthAids() {
        return JSON.parse(localStorage.getItem('health_aids') || '[]');
    }

    addHealthAid(aid) {
        const aids = this.getHealthAids();
        aid.id = Date.now();
        aid.createdAt = new Date().toISOString();
        aid.status = 'قيد المراجعة';
        aids.push(aid);
        localStorage.setItem('health_aids', JSON.stringify(aids));

        this.addActivity({
            type: 'health',
            title: 'إعانة صحية جديدة',
            description: `تم تسجيل طلب إعانة صحية بمبلغ ${aid.amount} دج`,
            icon: 'heartbeat',
            color: '#e74c3c'
        });

        return aid;
    }

    // وظائف الأنشطة
    getActivities() {
        return JSON.parse(localStorage.getItem('activities') || '[]');
    }

    addActivity(activity) {
        const activities = this.getActivities();
        activity.id = Date.now();
        activity.date = new Date().toISOString();
        activities.unshift(activity); // إضافة في المقدمة

        // الاحتفاظ بآخر 50 نشاط فقط
        if (activities.length > 50) {
            activities.splice(50);
        }

        localStorage.setItem('activities', JSON.stringify(activities));
        return activity;
    }

    // إحصائيات
    getStatistics() {
        const employees = this.getEmployees();
        const financialAids = this.getFinancialAids();
        const trips = this.getTrips();

        const totalBudget = financialAids.reduce((sum, aid) => sum + (aid.amount || 0), 0);

        return {
            totalEmployees: employees.length,
            totalBudget: totalBudget,
            totalAids: financialAids.length,
            totalTrips: trips.length,
            maleEmployees: employees.filter(emp => emp.gender === 'ذكر').length,
            femaleEmployees: employees.filter(emp => emp.gender === 'أنثى').length
        };
    }

    // تنسيق العملة
    formatCurrency(amount) {
        return new Intl.NumberFormat('ar-DZ', {
            style: 'currency',
            currency: 'DZD',
            minimumFractionDigits: 0
        }).format(amount).replace('DZD', 'دج');
    }

    // تنسيق التاريخ
    formatDate(date) {
        return new Date(date).toLocaleDateString('en-GB');
    }

    // وظائف أماكن العمل
    getWorkplaces() {
        return JSON.parse(localStorage.getItem('workplaces') || '[]');
    }

    addWorkplace(workplaceName) {
        const workplaces = this.getWorkplaces();
        if (!workplaces.includes(workplaceName)) {
            workplaces.push(workplaceName);
            workplaces.sort(); // ترتيب أبجدي
            localStorage.setItem('workplaces', JSON.stringify(workplaces));

            this.addActivity({
                type: 'workplace',
                title: 'إضافة مكان عمل جديد',
                description: `تم إضافة مكان العمل: ${workplaceName}`,
                icon: 'building',
                color: '#9b59b6'
            });

            return true;
        }
        return false; // مكان العمل موجود مسبقاً
    }

    deleteWorkplace(workplaceName) {
        const workplaces = this.getWorkplaces();
        const index = workplaces.indexOf(workplaceName);

        if (index !== -1) {
            // التحقق من وجود موظفين في هذا المكان
            const employees = this.getEmployees();
            const employeesInWorkplace = employees.filter(emp => emp.department === workplaceName);

            if (employeesInWorkplace.length > 0) {
                return { success: false, message: `لا يمكن حذف مكان العمل لوجود ${employeesInWorkplace.length} موظف/موظفة` };
            }

            workplaces.splice(index, 1);
            localStorage.setItem('workplaces', JSON.stringify(workplaces));

            this.addActivity({
                type: 'workplace',
                title: 'حذف مكان عمل',
                description: `تم حذف مكان العمل: ${workplaceName}`,
                icon: 'building',
                color: '#e74c3c'
            });

            return { success: true };
        }

        return { success: false, message: 'مكان العمل غير موجود' };
    }

    updateWorkplace(oldName, newName) {
        const workplaces = this.getWorkplaces();
        const index = workplaces.indexOf(oldName);

        if (index !== -1 && !workplaces.includes(newName)) {
            workplaces[index] = newName;
            workplaces.sort();
            localStorage.setItem('workplaces', JSON.stringify(workplaces));

            // تحديث أماكن عمل الموظفين
            const employees = this.getEmployees();
            employees.forEach(employee => {
                if (employee.department === oldName) {
                    employee.department = newName;
                }
            });
            localStorage.setItem('employees', JSON.stringify(employees));

            this.addActivity({
                type: 'workplace',
                title: 'تعديل مكان عمل',
                description: `تم تغيير مكان العمل من "${oldName}" إلى "${newName}"`,
                icon: 'edit',
                color: '#f39c12'
            });

            return true;
        }
        return false;
    }

    // وظائف الوكالات البنكية
    getBankBranches() {
        return JSON.parse(localStorage.getItem('bank_branches') || '[]');
    }

    addBankBranch(branchName) {
        const branches = this.getBankBranches();
        if (!branches.includes(branchName)) {
            branches.push(branchName);
            branches.sort(); // ترتيب أبجدي
            localStorage.setItem('bank_branches', JSON.stringify(branches));

            this.addActivity({
                type: 'bank_branch',
                title: 'إضافة وكالة بنكية جديدة',
                description: `تم إضافة الوكالة البنكية: ${branchName}`,
                icon: 'university',
                color: '#2ecc71'
            });

            return true;
        }
        return false; // الوكالة موجودة مسبقاً
    }

    deleteBankBranch(branchName) {
        const branches = this.getBankBranches();
        const index = branches.indexOf(branchName);

        if (index !== -1) {
            // التحقق من وجود موظفين يستخدمون هذه الوكالة
            const employees = this.getEmployees();
            const employeesWithBranch = employees.filter(emp => emp.bankBranch === branchName);

            if (employeesWithBranch.length > 0) {
                return { success: false, message: `لا يمكن حذف الوكالة البنكية لوجود ${employeesWithBranch.length} موظف/موظفة يستخدمونها` };
            }

            branches.splice(index, 1);
            localStorage.setItem('bank_branches', JSON.stringify(branches));

            this.addActivity({
                type: 'bank_branch',
                title: 'حذف وكالة بنكية',
                description: `تم حذف الوكالة البنكية: ${branchName}`,
                icon: 'university',
                color: '#e74c3c'
            });

            return { success: true };
        }

        return { success: false, message: 'الوكالة البنكية غير موجودة' };
    }

    updateBankBranch(oldName, newName) {
        const branches = this.getBankBranches();
        const index = branches.indexOf(oldName);

        if (index !== -1 && !branches.includes(newName)) {
            branches[index] = newName;
            branches.sort();
            localStorage.setItem('bank_branches', JSON.stringify(branches));

            // تحديث الوكالات البنكية للموظفين
            const employees = this.getEmployees();
            employees.forEach(employee => {
                if (employee.bankBranch === oldName) {
                    employee.bankBranch = newName;
                }
            });
            localStorage.setItem('employees', JSON.stringify(employees));

            this.addActivity({
                type: 'bank_branch',
                title: 'تعديل وكالة بنكية',
                description: `تم تغيير الوكالة البنكية من "${oldName}" إلى "${newName}"`,
                icon: 'edit',
                color: '#f39c12'
            });

            return true;
        }
        return false;
    }
}

// إنشاء مثيل من قاعدة البيانات
const db = new LocalDatabase();
