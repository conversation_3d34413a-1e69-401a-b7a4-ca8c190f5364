<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القروض والسلفيات - نظام إدارة لجنة الخدمات الاجتماعية</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <i class="fas fa-hands-helping"></i>
                    <h1>نظام إدارة لجنة الخدمات الاجتماعية</h1>
                </div>
                <div class="user-info">
                    <span class="date" id="currentDate"></span>
                    <div class="user-profile">
                        <i class="fas fa-user-circle"></i>
                        <span>مدير النظام</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="navbar">
        <div class="container">
            <ul class="nav-menu">
                <li><a href="index.html"><i class="fas fa-home"></i> الرئيسية</a></li>
                <li><a href="employees.html"><i class="fas fa-users"></i> الموظفين</a></li>
                <li><a href="financial.html"><i class="fas fa-money-bill-wave"></i> الخدمات المالية</a></li>
                <li><a href="loans.html" class="active"><i class="fas fa-hand-holding-usd"></i> القروض والسلفيات</a></li>
                <li><a href="restaurant.html"><i class="fas fa-utensils"></i> المطعم</a></li>
                <li><a href="trips.html"><i class="fas fa-plane"></i> الرحلات</a></li>
                <li><a href="events.html"><i class="fas fa-calendar-alt"></i> الفعاليات</a></li>
                <li><a href="health.html"><i class="fas fa-heartbeat"></i> الإعانات الصحية</a></li>
                <li><a href="reports.html"><i class="fas fa-chart-bar"></i> التقارير</a></li>
            </ul>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h2><i class="fas fa-hand-holding-usd"></i> إدارة القروض والسلفيات</h2>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="showModal('addLoanModal')">
                        <i class="fas fa-plus"></i> إضافة قرض جديد
                    </button>
                    <button class="btn btn-success" onclick="exportLoansReport()">
                        <i class="fas fa-file-excel"></i> تصدير التقرير
                    </button>
                </div>
            </div>

            <!-- Sharia Compliance Notice -->
            <div class="sharia-notice">
                <i class="fas fa-info-circle"></i>
                <strong>ملاحظة مهمة:</strong> جميع القروض والسلفيات في هذا النظام بدون فوائد، متوافقة مع الأحكام الشرعية الإسلامية
            </div>

            <!-- Loans Statistics -->
            <section class="loans-stats">
                <div class="stats-grid">
                    <div class="stat-card total-loans">
                        <div class="stat-icon" style="background: #3498db;">
                            <i class="fas fa-hand-holding-usd"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalLoansCount">0</h3>
                            <p>إجمالي القروض</p>
                        </div>
                    </div>
                    <div class="stat-card total-amount">
                        <div class="stat-icon" style="background: #e74c3c;">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalLoansAmount">0 دج</h3>
                            <p>إجمالي المبالغ المقترضة</p>
                        </div>
                    </div>
                    <div class="stat-card paid-amount">
                        <div class="stat-icon" style="background: #27ae60;">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalPaidAmount">0 دج</h3>
                            <p>إجمالي المبالغ المسددة</p>
                        </div>
                    </div>
                    <div class="stat-card remaining-amount">
                        <div class="stat-icon" style="background: #f39c12;">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h3 id="totalRemainingAmount">0 دج</h3>
                            <p>إجمالي المبالغ المتبقية</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Quick Actions -->
            <section class="quick-actions">
                <div class="actions-grid">
                    <div class="action-card" onclick="showActiveLoans()">
                        <i class="fas fa-list-alt"></i>
                        <h4>القروض النشطة</h4>
                        <p>عرض القروض التي لم يتم سدادها بالكامل</p>
                    </div>
                    <div class="action-card" onclick="showPaidLoans()">
                        <i class="fas fa-check-double"></i>
                        <h4>القروض المسددة</h4>
                        <p>عرض القروض المسددة بالكامل</p>
                    </div>
                    <div class="action-card" onclick="showOverduePayments()">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h4>الأقساط المتأخرة</h4>
                        <p>عرض الأقساط المستحقة والمتأخرة</p>
                    </div>
                    <div class="action-card" onclick="showPaymentSchedule()">
                        <i class="fas fa-calendar-check"></i>
                        <h4>جدولة الدفعات</h4>
                        <p>عرض جدول الدفعات المستقبلية</p>
                    </div>
                </div>
            </section>

            <!-- Loans Management Section -->
            <section class="loans-management">
                <div class="section-header">
                    <h3><i class="fas fa-list"></i> إدارة القروض</h3>
                    <div class="section-actions">
                        <div class="search-input">
                            <input type="text" id="loansSearch" placeholder="البحث في القروض..." onkeyup="searchLoans()">
                            <i class="fas fa-search"></i>
                        </div>
                        <select id="loansFilter" onchange="filterLoans()">
                            <option value="">جميع القروض</option>
                            <option value="نشط">القروض النشطة</option>
                            <option value="مسدد">القروض المسددة</option>
                            <option value="قرض شخصي">القروض الشخصية</option>
                            <option value="سلفة راتب">سلف الراتب</option>
                            <option value="قرض سكن">قروض السكن</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>رقم القرض</th>
                                <th>المستفيد</th>
                                <th>نوع القرض</th>
                                <th>المبلغ الإجمالي</th>
                                <th>المبلغ المسدد</th>
                                <th>المبلغ المتبقي</th>
                                <th>القسط الشهري</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="loansTableBody">
                            <!-- Loans data will be loaded here -->
                        </tbody>
                    </table>
                </div>
            </section>

            <!-- Payment Schedule Section -->
            <section class="payment-schedule-section" style="display: none;" id="paymentScheduleSection">
                <div class="section-header">
                    <h3><i class="fas fa-calendar-check"></i> جدولة الدفعات</h3>
                    <div class="section-actions">
                        <button class="btn btn-secondary btn-sm" onclick="hidePaymentSchedule()">
                            <i class="fas fa-times"></i> إغلاق
                        </button>
                    </div>
                </div>

                <div class="schedule-container" id="scheduleContainer">
                    <!-- Payment schedule will be loaded here -->
                </div>
            </section>
        </div>
    </main>

    <!-- Add Loan Modal -->
    <div id="addLoanModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-plus"></i> إضافة قرض جديد</h3>
                <span class="close" onclick="hideModal('addLoanModal')">&times;</span>
            </div>
            <form id="addLoanForm" class="modal-body">
                <div class="form-grid">
                    <div class="form-group">
                        <label for="loanBeneficiary">المستفيد *</label>
                        <select id="loanBeneficiary" name="beneficiary" required>
                            <option value="">اختر الموظف</option>
                            <!-- Options will be loaded dynamically -->
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="loanType">نوع القرض *</label>
                        <select id="loanType" name="type" required>
                            <option value="">اختر نوع القرض</option>
                            <option value="قرض شخصي">قرض شخصي</option>
                            <option value="سلفة راتب">سلفة راتب</option>
                            <option value="قرض سكن">قرض سكن</option>
                            <option value="قرض تعليمي">قرض تعليمي</option>
                            <option value="قرض طارئ">قرض طارئ</option>
                            <option value="قرض زواج">قرض زواج</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="loanAmount">مبلغ القرض (دج) *</label>
                        <input type="number" id="loanAmount" name="amount" required min="1000" step="1000">
                    </div>
                    <div class="form-group">
                        <label for="loanInstallments">عدد الأقساط *</label>
                        <input type="number" id="loanInstallments" name="installments" required min="1" max="60">
                    </div>

                    <div class="form-group">
                        <label for="loanStartDate">تاريخ بداية القرض *</label>
                        <input type="date" id="loanStartDate" name="startDate" required>
                    </div>
                    <div class="form-group full-width">
                        <label for="loanPurpose">الغرض من القرض *</label>
                        <textarea id="loanPurpose" name="purpose" rows="3" required placeholder="اذكر الغرض من القرض..."></textarea>
                    </div>
                    <div class="form-group full-width">
                        <div class="loan-calculation-display" id="loanCalculationDisplay">
                            <div class="calculation-item">
                                <label>القسط الشهري المحسوب:</label>
                                <span id="monthlyPaymentDisplay">0 دج</span>
                            </div>
                            <div class="calculation-item">
                                <label>إجمالي المبلغ:</label>
                                <span id="totalAmountDisplay">0 دج</span>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('addLoanModal')">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveLoan()">حفظ القرض</button>
            </div>
        </div>
    </div>

    <!-- Pay Installment Modal -->
    <div id="payInstallmentModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-money-bill"></i> دفع قسط القرض</h3>
                <span class="close" onclick="hideModal('payInstallmentModal')">&times;</span>
            </div>
            <div class="modal-body">
                <input type="hidden" id="paymentLoanId">

                <!-- Loan Information -->
                <div class="loan-info-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات القرض</h4>
                    <div class="info-grid">
                        <div class="info-item">
                            <label>اسم الموظف:</label>
                            <span id="paymentEmployeeName">-</span>
                        </div>
                        <div class="info-item">
                            <label>نوع القرض:</label>
                            <span id="paymentLoanType">-</span>
                        </div>
                        <div class="info-item">
                            <label>المبلغ الإجمالي:</label>
                            <span id="paymentTotalAmount">-</span>
                        </div>
                        <div class="info-item">
                            <label>المبلغ المسدد:</label>
                            <span id="paymentPaidAmount">-</span>
                        </div>
                        <div class="info-item">
                            <label>المبلغ المتبقي:</label>
                            <span id="paymentRemainingAmount" class="remaining-amount">-</span>
                        </div>
                        <div class="info-item">
                            <label>القسط الشهري:</label>
                            <span id="paymentMonthlyAmount">-</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Form -->
                <form id="payInstallmentForm">
                    <div class="form-grid">
                        <div class="form-group">
                            <label for="paymentInstallmentAmount">مبلغ القسط (دج) *</label>
                            <input type="number" id="paymentInstallmentAmount" name="amount" required min="0" step="10">
                        </div>
                        <div class="form-group">
                            <label for="paymentDate">تاريخ الدفع *</label>
                            <input type="date" id="paymentDate" name="date" required>
                        </div>
                        <div class="form-group full-width">
                            <label for="paymentNotes">ملاحظات</label>
                            <textarea id="paymentNotes" name="notes" rows="3" placeholder="ملاحظات حول الدفع..."></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('payInstallmentModal')">إلغاء</button>
                <button type="button" class="btn btn-success" onclick="saveInstallmentPayment()">
                    <i class="fas fa-check"></i> تأكيد الدفع
                </button>
            </div>
        </div>
    </div>

    <!-- Loan Details Modal -->
    <div id="loanDetailsModal" class="modal">
        <div class="modal-content" style="max-width: 900px;">
            <div class="modal-header">
                <h3><i class="fas fa-file-alt"></i> تفاصيل القرض</h3>
                <span class="close" onclick="hideModal('loanDetailsModal')">&times;</span>
            </div>
            <div class="modal-body">
                <!-- Loan Details -->
                <div class="loan-details-section">
                    <h4><i class="fas fa-info-circle"></i> معلومات القرض</h4>
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>اسم الموظف:</label>
                            <span id="detailsEmployeeName">-</span>
                        </div>
                        <div class="detail-item">
                            <label>نوع القرض:</label>
                            <span id="detailsLoanType">-</span>
                        </div>
                        <div class="detail-item">
                            <label>المبلغ الإجمالي:</label>
                            <span id="detailsTotalAmount">-</span>
                        </div>
                        <div class="detail-item">
                            <label>المبلغ المسدد:</label>
                            <span id="detailsPaidAmount">-</span>
                        </div>
                        <div class="detail-item">
                            <label>المبلغ المتبقي:</label>
                            <span id="detailsRemainingAmount">-</span>
                        </div>
                        <div class="detail-item">
                            <label>القسط الشهري:</label>
                            <span id="detailsMonthlyInstallment">-</span>
                        </div>
                        <div class="detail-item">
                            <label>عدد الأقساط:</label>
                            <span id="detailsInstallments">-</span>
                        </div>

                        <div class="detail-item">
                            <label>تاريخ البداية:</label>
                            <span id="detailsStartDate">-</span>
                        </div>
                        <div class="detail-item">
                            <label>الحالة:</label>
                            <span id="detailsStatus">-</span>
                        </div>
                        <div class="detail-item full-width">
                            <label>الغرض من القرض:</label>
                            <span id="detailsPurpose">-</span>
                        </div>
                    </div>
                </div>

                <!-- Payment History -->
                <div class="payment-history-section">
                    <h4><i class="fas fa-history"></i> سجل الدفعات</h4>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>رقم الدفعة</th>
                                    <th>المبلغ</th>
                                    <th>تاريخ الدفع</th>
                                    <th>ملاحظات</th>
                                </tr>
                            </thead>
                            <tbody id="paymentsTableBody">
                                <!-- Payment history will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="hideModal('loanDetailsModal')">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="exportLoanDetails()">
                    <i class="fas fa-download"></i> تصدير التفاصيل
                </button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 نظام إدارة لجنة الخدمات الاجتماعية - جميع الحقوق محفوظة</p>
        </div>
    </footer>

    <script src="database.js"></script>
    <script src="script.js"></script>
    <script src="loans.js"></script>
</body>
</html>
