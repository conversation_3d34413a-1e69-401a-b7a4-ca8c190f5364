# نظام إدارة لجنة الخدمات الاجتماعية

نظام ويب شامل لإدارة جميع خدمات لجنة الخدمات الاجتماعية للهيئات العمومية باللغة العربية مع دعم العملة الجزائرية والتواريخ اللاتينية.

## المميزات الرئيسية

### 🏢 إدارة الموظفين
- إضافة وتعديل وحذف بيانات الموظفين مع صورهم
- إدارة أرقام الحسابات البنكية والبريدية ووكالاتها
- استيراد قوائم الموظفين من ملفات Excel
- البحث والفلترة المتقدمة
- إحصائيات شاملة عن الموظفين (النساء/الرجال)
- تتبع عدد الأبناء المتمدرسين بمختلف الأطوار (ابتدائي، متوسط، ثانوي، جامعي)

### 💰 الخدمات المالية
- **الإعانات المالية**: تسجيل الإعانات الواردة من الهيئات المختلفة
- **المنح والهدايا**: إدارة المنح الموسمية (عيد الأضحى، الدخول المدرسي، عيد المرأة، وأخرى)
- **القروض والسلفيات**: منح القروض ومتابعة الأقساط الشهرية التلقائية والاتصال بمصلحة الأجور

### 🛍️ التجهيزات الكهرومنزلية
- إدارة مخزون التجهيزات (ثلاجات، غسالات، أفران...)
- طلبات التجهيزات مع إمكانية الدفع بالتقسيط
- متابعة حالة الطلبات

### ✈️ الرحلات والاصطياف
- تنظيم رحلات داخلية وخارجية (تونس، تركيا...)
- إدارة التسجيل والدفع
- متابعة عدد المشاركين

### 🎉 الفعاليات والحفلات
- تنظيم حفلات تكريمية (عيد المرأة...)
- إدارة قوائم المدعوين
- متابعة الحضور والتكاليف

### 🏥 الإعانات الصحية
- طلبات الإعانات الصحية
- متابعة الملفات الطبية
- الموافقة على الطلبات

### 📊 التقارير والإحصائيات
- تقارير مالية شاملة
- إحصائيات الموظفين
- تقارير دورية (شهرية، فصلية، سنوية)
- إمكانية التصدير والطباعة

## التقنيات المستخدمة

- **HTML5**: هيكل الصفحات
- **CSS3**: التصميم والتنسيق مع دعم RTL
- **JavaScript**: الوظائف التفاعلية
- **LocalStorage**: تخزين البيانات محلياً
- **Font Awesome**: الأيقونات
- **Google Fonts (Cairo)**: الخط العربي

## المتطلبات

- متصفح ويب حديث يدعم HTML5 و CSS3 و JavaScript
- لا يتطلب خادم ويب (يعمل محلياً)

## التشغيل

1. تحميل جميع الملفات في مجلد واحد
2. فتح ملف `index.html` في المتصفح
3. أو تشغيل خادم ويب محلي:
   ```bash
   python -m http.server 8000
   ```
   ثم فتح `http://localhost:8000` في المتصفح

## هيكل الملفات

```
sosiel2025/
├── index.html              # الصفحة الرئيسية
├── employees.html          # صفحة إدارة الموظفين
├── financial.html          # صفحة الخدمات المالية
├── equipment.html          # صفحة التجهيزات (قيد التطوير)
├── trips.html             # صفحة الرحلات (قيد التطوير)
├── events.html            # صفحة الفعاليات (قيد التطوير)
├── health.html            # صفحة الإعانات الصحية (قيد التطوير)
├── reports.html           # صفحة التقارير
├── styles.css             # ملف التنسيق الرئيسي
├── script.js              # الوظائف الأساسية
├── database.js            # محاكاة قاعدة البيانات
├── employees.js           # وظائف إدارة الموظفين
├── financial.js           # وظائف الخدمات المالية
├── reports.js             # وظائف التقارير
└── README.md              # هذا الملف
```

## الميزات التقنية

### واجهة المستخدم
- تصميم متجاوب يعمل على جميع الأجهزة
- دعم كامل للغة العربية مع اتجاه RTL
- ألوان وتصميم احترافي
- أيقونات واضحة ومعبرة

### إدارة البيانات
- تخزين محلي باستخدام LocalStorage
- بيانات تجريبية للاختبار
- نظام إدارة الأنشطة والسجلات
- تنسيق العملة بالدينار الجزائري
- تنسيق التواريخ باللاتينية

### الأمان والموثوقية
- التحقق من صحة البيانات
- رسائل تأكيد للعمليات الحساسة
- نسخ احتياطية للبيانات
- معالجة الأخطاء

## الاستخدام

### إضافة موظف جديد
1. الانتقال إلى صفحة "الموظفين"
2. النقر على "إضافة موظف جديد"
3. ملء البيانات المطلوبة
4. إضافة بيانات الأطفال (اختياري)
5. رفع صورة الموظف (اختياري)
6. حفظ البيانات

### إضافة إعانة مالية
1. الانتقال إلى صفحة "الخدمات المالية"
2. النقر على "إضافة إعانة مالية"
3. اختيار المستفيد ونوع الإعانة
4. تحديد المبلغ والتاريخ
5. إضافة الوصف والمستندات
6. حفظ الإعانة

### إنشاء تقرير
1. الانتقال إلى صفحة "التقارير"
2. اختيار نوع التقرير والفترة الزمنية
3. النقر على "إنشاء تقرير"
4. مراجعة البيانات
5. طباعة أو تصدير التقرير

## التطوير المستقبلي

- إضافة قاعدة بيانات حقيقية
- تطوير API للتكامل مع أنظمة أخرى
- إضافة نظام المصادقة والصلاحيات
- تطوير تطبيق الهاتف المحمول
- إضافة المزيد من التقارير والإحصائيات
- تحسين أداء النظام
- إضافة النسخ الاحتياطي التلقائي

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- مراجعة هذا الملف للتعليمات
- فحص وحدة تحكم المتصفح للأخطاء
- التأكد من تحديث المتصفح

## الترخيص

هذا النظام مطور خصيصاً لإدارة لجنة الخدمات الاجتماعية.
جميع الحقوق محفوظة © 2025

## ملاحظات مهمة

- النظام يستخدم التخزين المحلي، لذا البيانات محفوظة في المتصفح فقط
- يُنصح بعمل نسخ احتياطية دورية للبيانات
- النظام مصمم للاستخدام المحلي وليس للإنتاج على الإنترنت
- بعض الصفحات قيد التطوير وستكتمل في التحديثات القادمة
