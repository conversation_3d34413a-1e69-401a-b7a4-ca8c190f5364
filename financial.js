// Treasury Management System - Financial Year Based

// Financial year configuration
const FINANCIAL_YEARS = ['2024', '2025', '2026'];
let currentFinancialYear = '2025'; // Default financial year

// Initialize treasury data when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeTreasuryData();
    setupFinancialYearSelector();
    setupTreasuryEventListeners();
    displayFinancialOverview();
    displayRevenues();
    displayExpenses();
    displayLoans();
    displayTreasuryTransactions();
    loadEmployeeOptions();
    setCurrentDate();
});

// Initialize treasury data
function initializeTreasuryData() {
    // Initialize data for each financial year
    FINANCIAL_YEARS.forEach(year => {
        if (!localStorage.getItem(`revenues_${year}`)) {
            localStorage.setItem(`revenues_${year}`, JSON.stringify([]));
        }
        if (!localStorage.getItem(`expenses_${year}`)) {
            localStorage.setItem(`expenses_${year}`, JSON.stringify([]));
        }
        if (!localStorage.getItem(`treasuryTransactions_${year}`)) {
            localStorage.setItem(`treasuryTransactions_${year}`, JSON.stringify([]));
        }
        if (!localStorage.getItem(`openingBalance_${year}`)) {
            localStorage.setItem(`openingBalance_${year}`, '0');
        }
        if (!localStorage.getItem(`loans_${year}`)) {
            localStorage.setItem(`loans_${year}`, JSON.stringify([]));
        }
        if (!localStorage.getItem(`loanPayments_${year}`)) {
            localStorage.setItem(`loanPayments_${year}`, JSON.stringify([]));
        }
    });

    // Set current financial year from localStorage or default
    const savedYear = localStorage.getItem('currentFinancialYear');
    if (savedYear && FINANCIAL_YEARS.includes(savedYear)) {
        currentFinancialYear = savedYear;
    }
}

// Setup financial year selector
function setupFinancialYearSelector() {
    // Add financial year selector to page header
    const pageHeader = document.querySelector('.page-header');
    if (pageHeader && !document.getElementById('financialYearSelector')) {
        const yearSelector = document.createElement('div');
        yearSelector.className = 'financial-year-selector';
        yearSelector.innerHTML = `
            <label for="financialYear">السنة المالية:</label>
            <select id="financialYear" onchange="changeFinancialYear(this.value)">
                ${FINANCIAL_YEARS.map(year =>
                    `<option value="${year}" ${year === currentFinancialYear ? 'selected' : ''}>${year}</option>`
                ).join('')}
            </select>
        `;
        pageHeader.appendChild(yearSelector);
    }
}

// Change financial year
function changeFinancialYear(year) {
    if (FINANCIAL_YEARS.includes(year)) {
        currentFinancialYear = year;
        localStorage.setItem('currentFinancialYear', year);

        // Refresh all displays
        displayFinancialOverview();
        displayRevenues();
        displayExpenses();
        displayLoans();
        displayTreasuryTransactions();

        showAlert(`تم التبديل إلى السنة المالية ${year}`, 'success');
    }
}

// Get current year data
function getCurrentYearRevenues() {
    return JSON.parse(localStorage.getItem(`revenues_${currentFinancialYear}`) || '[]');
}

function getCurrentYearExpenses() {
    return JSON.parse(localStorage.getItem(`expenses_${currentFinancialYear}`) || '[]');
}

function getCurrentYearTransactions() {
    return JSON.parse(localStorage.getItem(`treasuryTransactions_${currentFinancialYear}`) || '[]');
}

function getCurrentYearOpeningBalance() {
    return parseFloat(localStorage.getItem(`openingBalance_${currentFinancialYear}`) || '0');
}

function getCurrentYearLoans() {
    return JSON.parse(localStorage.getItem(`loans_${currentFinancialYear}`) || '[]');
}

function getCurrentYearLoanPayments() {
    return JSON.parse(localStorage.getItem(`loanPayments_${currentFinancialYear}`) || '[]');
}

// Setup event listeners
function setupTreasuryEventListeners() {
    // Set current date as default
    const today = new Date().toISOString().split('T')[0];
    const revenueDate = document.getElementById('revenueDate');
    const expenseDate = document.getElementById('expenseDate');

    if (revenueDate) revenueDate.value = today;
    if (expenseDate) expenseDate.value = today;
}

// Load employee options for dropdowns
async function loadEmployeeOptions() {
    try {
        const employees = await db.getEmployees();

        // Load loan employee options
        const loanEmployeeSelect = document.getElementById('loanEmployee');
        if (loanEmployeeSelect) {
            loanEmployeeSelect.innerHTML = '<option value="">اختر الموظف</option>';
            employees.forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = `${employee.name} - ${employee.department}`;
                loanEmployeeSelect.appendChild(option);
            });
        }
    } catch (error) {
        console.error('Error loading employee options:', error);
        showAlert('خطأ في تحميل قائمة الموظفين', 'error');
    }
}

// Set current date
function setCurrentDate() {
    const currentMonthElement = document.getElementById('currentMonth');
    if (currentMonthElement) {
        const today = new Date();
        const monthNames = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];
        currentMonthElement.textContent = `${monthNames[today.getMonth()]} ${currentFinancialYear}`;
    }
}

// Display financial overview
function displayFinancialOverview() {
    const revenues = getCurrentYearRevenues();
    const expenses = getCurrentYearExpenses();
    const openingBalance = getCurrentYearOpeningBalance();

    // Calculate totals
    const totalRevenue = revenues.reduce((sum, revenue) => sum + (revenue.amount || 0), 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
    const currentBalance = openingBalance + totalRevenue - totalExpenses;

    // Calculate monthly revenue
    const currentMonth = new Date().getMonth();
    const monthlyRevenue = revenues
        .filter(revenue => {
            const revenueDate = new Date(revenue.date);
            return revenueDate.getMonth() === currentMonth;
        })
        .reduce((sum, revenue) => sum + (revenue.amount || 0), 0);

    // Update DOM elements
    updateElement('totalRevenue', `${totalRevenue.toLocaleString()} دج`);
    updateElement('totalExpenses', `${totalExpenses.toLocaleString()} دج`);
    updateElement('monthlyRevenue', `${monthlyRevenue.toLocaleString()} دج`);

    const currentBalanceEl = document.getElementById('currentBalance');
    if (currentBalanceEl) {
        currentBalanceEl.textContent = `${currentBalance.toLocaleString()} دج`;
        currentBalanceEl.className = currentBalance >= 0 ? 'positive-balance' : 'negative-balance';
    }

    // Update treasury summary
    updateElement('openingBalance', `${openingBalance.toLocaleString()} دج`);
    updateElement('treasuryTotalRevenue', `${totalRevenue.toLocaleString()} دج`);
    updateElement('treasuryTotalExpenses', `${totalExpenses.toLocaleString()} دج`);

    const treasuryCurrentBalanceEl = document.getElementById('treasuryCurrentBalance');
    if (treasuryCurrentBalanceEl) {
        treasuryCurrentBalanceEl.textContent = `${currentBalance.toLocaleString()} دج`;
        treasuryCurrentBalanceEl.className = currentBalance >= 0 ? 'current-balance positive' : 'current-balance negative';
    }
}

// Helper function to update element
function updateElement(id, value) {
    const element = document.getElementById(id);
    if (element) element.textContent = value;
}

// Save revenue
function saveRevenue() {
    const source = document.getElementById('revenueSource').value.trim();
    const type = document.getElementById('revenueType').value;
    const amount = parseFloat(document.getElementById('revenueAmount').value) || 0;
    const date = document.getElementById('revenueDate').value;
    const status = document.getElementById('revenueStatus').value;
    const category = document.getElementById('revenueCategory').value;
    const description = document.getElementById('revenueDescription').value.trim();
    const notes = document.getElementById('revenueNotes').value.trim();

    // Validation
    if (!source || !type || !amount || !date || !description) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (amount <= 0) {
        showAlert('المبلغ يجب أن يكون أكبر من صفر', 'error');
        return;
    }

    // Create revenue object
    const revenue = {
        id: Date.now(),
        source: source,
        type: type,
        amount: amount,
        date: date,
        status: status,
        category: category,
        description: description,
        notes: notes,
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    // Save revenue for current financial year
    const revenues = getCurrentYearRevenues();
    revenues.push(revenue);
    localStorage.setItem(`revenues_${currentFinancialYear}`, JSON.stringify(revenues));

    // Add to treasury transactions
    addTreasuryTransaction('إيراد', amount, `${type}: ${source}`, date);

    showAlert(`تم حفظ الإيراد بنجاح للسنة المالية ${currentFinancialYear}`, 'success');
    hideModal('addRevenueModal');
    resetRevenueForm();
    displayFinancialOverview();
    displayRevenues();
    displayTreasuryTransactions();
}

// Save expense
function saveExpense() {
    const type = document.getElementById('expenseType').value;
    const amount = parseFloat(document.getElementById('expenseAmount').value) || 0;
    const date = document.getElementById('expenseDate').value;
    const beneficiary = document.getElementById('expenseBeneficiary').value.trim();
    const status = document.getElementById('expenseStatus').value;
    const category = document.getElementById('expenseCategory').value;
    const description = document.getElementById('expenseDescription').value.trim();
    const notes = document.getElementById('expenseNotes').value.trim();

    // Validation
    if (!type || !amount || !date || !description) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (amount <= 0) {
        showAlert('المبلغ يجب أن يكون أكبر من صفر', 'error');
        return;
    }

    // Create expense object
    const expense = {
        id: Date.now(),
        type: type,
        amount: amount,
        date: date,
        beneficiary: beneficiary,
        status: status,
        category: category,
        description: description,
        notes: notes,
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    // Save expense for current financial year
    const expenses = getCurrentYearExpenses();
    expenses.push(expense);
    localStorage.setItem(`expenses_${currentFinancialYear}`, JSON.stringify(expenses));

    // Add to treasury transactions
    addTreasuryTransaction('مصروف', -amount, `${type}: ${description}`, date);

    showAlert(`تم حفظ المصروف بنجاح للسنة المالية ${currentFinancialYear}`, 'success');
    hideModal('addExpenseModal');
    resetExpenseForm();
    displayFinancialOverview();
    displayExpenses();
    displayTreasuryTransactions();
}

// Add treasury transaction
function addTreasuryTransaction(type, amount, description, date) {
    const transactions = getCurrentYearTransactions();
    const revenues = getCurrentYearRevenues();
    const expenses = getCurrentYearExpenses();
    const openingBalance = getCurrentYearOpeningBalance();

    // Calculate balance after transaction
    const totalRevenue = revenues.reduce((sum, revenue) => sum + (revenue.amount || 0), 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + (expense.amount || 0), 0);
    const balanceAfter = openingBalance + totalRevenue - totalExpenses;

    const transaction = {
        id: Date.now(),
        type: type,
        amount: amount,
        description: description,
        date: date,
        balanceAfter: balanceAfter,
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    transactions.push(transaction);
    localStorage.setItem(`treasuryTransactions_${currentFinancialYear}`, JSON.stringify(transactions));
}

// Reset forms
function resetRevenueForm() {
    document.getElementById('addRevenueForm').reset();
    document.getElementById('revenueStatus').value = 'مؤكد';
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('revenueDate').value = today;
}

function resetExpenseForm() {
    document.getElementById('addExpenseForm').reset();
    document.getElementById('expenseStatus').value = 'مدفوع';
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('expenseDate').value = today;
}

// Format date for display
function formatDate(dateString) {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-DZ');
}

// Format currency
function formatCurrency(amount) {
    return `${amount.toLocaleString()} دج`;
}

// Get status class for styling
function getStatusClass(status) {
    switch(status) {
        case 'مؤكد':
        case 'مدفوع': return 'confirmed';
        case 'معلق': return 'pending';
        case 'مستلم': return 'received';
        case 'مؤجل': return 'postponed';
        default: return '';
    }
}

// Show tab
function showTab(tabName) {
    // Hide all tabs
    const tabs = document.querySelectorAll('.tab-content');
    tabs.forEach(tab => tab.classList.remove('active'));

    // Remove active class from all buttons
    const buttons = document.querySelectorAll('.tab-btn');
    buttons.forEach(btn => btn.classList.remove('active'));

    // Show selected tab
    const selectedTab = document.getElementById(`${tabName}-tab`);
    if (selectedTab) {
        selectedTab.classList.add('active');
    }

    // Add active class to clicked button
    const clickedButton = event.target.closest('.tab-btn');
    if (clickedButton) {
        clickedButton.classList.add('active');
    }
}

// Display revenues
function displayRevenues() {
    const revenues = getCurrentYearRevenues();
    const tableBody = document.getElementById('revenuesTableBody');

    if (!tableBody) return;

    if (revenues.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-arrow-up fa-2x"></i>
                        <h3>لا توجد إيرادات مسجلة</h3>
                        <p>اضغط على "إضافة إيراد جديد" لبدء إضافة الإيرادات</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    revenues.sort((a, b) => new Date(b.date) - new Date(a.date));

    tableBody.innerHTML = revenues.map(revenue => `
        <tr>
            <td><strong>${revenue.source}</strong></td>
            <td>
                <span class="revenue-type-badge">${revenue.type}</span>
            </td>
            <td><strong>${formatCurrency(revenue.amount)}</strong></td>
            <td>${formatDate(revenue.date)}</td>
            <td>${revenue.description}</td>
            <td>
                <span class="status-badge ${getStatusClass(revenue.status)}">
                    ${revenue.status}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon btn-info" onclick="viewRevenue(${revenue.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-warning" onclick="editRevenue(${revenue.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteRevenue(${revenue.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Display expenses
function displayExpenses() {
    const expenses = getCurrentYearExpenses();
    const tableBody = document.getElementById('expensesTableBody');

    if (!tableBody) return;

    if (expenses.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-arrow-down fa-2x"></i>
                        <h3>لا توجد مصروفات مسجلة</h3>
                        <p>اضغط على "إضافة مصروف جديد" لبدء إضافة المصروفات</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    expenses.sort((a, b) => new Date(b.date) - new Date(a.date));

    tableBody.innerHTML = expenses.map(expense => `
        <tr>
            <td>
                <span class="expense-type-badge">${expense.type}</span>
            </td>
            <td><strong>${formatCurrency(expense.amount)}</strong></td>
            <td>${formatDate(expense.date)}</td>
            <td>${expense.beneficiary || '-'}</td>
            <td>${expense.description}</td>
            <td>
                <span class="status-badge ${getStatusClass(expense.status)}">
                    ${expense.status}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon btn-info" onclick="viewExpense(${expense.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-warning" onclick="editExpense(${expense.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteExpense(${expense.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Display treasury transactions
function displayTreasuryTransactions() {
    const transactions = getCurrentYearTransactions();
    const tableBody = document.getElementById('treasuryTransactionsBody');

    if (!tableBody) return;

    if (transactions.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-exchange-alt fa-2x"></i>
                        <h3>لا توجد معاملات مالية</h3>
                        <p>ستظهر المعاملات هنا عند إضافة إيرادات أو مصروفات</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // Show only last 20 transactions
    const recentTransactions = transactions.slice(0, 20);

    tableBody.innerHTML = recentTransactions.map(transaction => `
        <tr>
            <td>${formatDate(transaction.date)}</td>
            <td>
                <span class="transaction-type ${transaction.type === 'إيراد' ? 'revenue' : 'expense'}">
                    <i class="fas fa-${transaction.type === 'إيراد' ? 'arrow-up' : 'arrow-down'}"></i>
                    ${transaction.type}
                </span>
            </td>
            <td>
                <span class="${transaction.amount >= 0 ? 'positive-amount' : 'negative-amount'}">
                    ${formatCurrency(Math.abs(transaction.amount))}
                </span>
            </td>
            <td>${transaction.description}</td>
            <td>
                <strong class="${transaction.balanceAfter >= 0 ? 'positive-balance' : 'negative-balance'}">
                    ${formatCurrency(transaction.balanceAfter)}
                </strong>
            </td>
        </tr>
    `).join('');
}

// Search and filter functions
function searchRevenues() {
    const searchTerm = document.getElementById('revenueSearch').value.toLowerCase();
    const revenues = getCurrentYearRevenues();

    const filteredRevenues = revenues.filter(revenue =>
        revenue.source.toLowerCase().includes(searchTerm) ||
        revenue.type.toLowerCase().includes(searchTerm) ||
        revenue.description.toLowerCase().includes(searchTerm)
    );

    displayFilteredRevenues(filteredRevenues);
}

function filterRevenues() {
    const filterValue = document.getElementById('revenueFilter').value;
    const revenues = getCurrentYearRevenues();

    let filteredRevenues = revenues;

    if (filterValue) {
        filteredRevenues = revenues.filter(revenue => revenue.type === filterValue);
    }

    displayFilteredRevenues(filteredRevenues);
}

function displayFilteredRevenues(revenues) {
    const tableBody = document.getElementById('revenuesTableBody');

    if (!tableBody) return;

    if (revenues.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على إيرادات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    revenues.sort((a, b) => new Date(b.date) - new Date(a.date));

    tableBody.innerHTML = revenues.map(revenue => `
        <tr>
            <td><strong>${revenue.source}</strong></td>
            <td>
                <span class="revenue-type-badge">${revenue.type}</span>
            </td>
            <td><strong>${formatCurrency(revenue.amount)}</strong></td>
            <td>${formatDate(revenue.date)}</td>
            <td>${revenue.description}</td>
            <td>
                <span class="status-badge ${getStatusClass(revenue.status)}">
                    ${revenue.status}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon btn-info" onclick="viewRevenue(${revenue.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-warning" onclick="editRevenue(${revenue.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteRevenue(${revenue.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Search and filter expenses
function searchExpenses() {
    const searchTerm = document.getElementById('expenseSearch').value.toLowerCase();
    const expenses = getCurrentYearExpenses();

    const filteredExpenses = expenses.filter(expense =>
        expense.type.toLowerCase().includes(searchTerm) ||
        expense.description.toLowerCase().includes(searchTerm) ||
        (expense.beneficiary && expense.beneficiary.toLowerCase().includes(searchTerm))
    );

    displayFilteredExpenses(filteredExpenses);
}

function filterExpenses() {
    const filterValue = document.getElementById('expenseFilter').value;
    const expenses = getCurrentYearExpenses();

    let filteredExpenses = expenses;

    if (filterValue) {
        filteredExpenses = expenses.filter(expense => expense.type === filterValue);
    }

    displayFilteredExpenses(filteredExpenses);
}

function displayFilteredExpenses(expenses) {
    const tableBody = document.getElementById('expensesTableBody');

    if (!tableBody) return;

    if (expenses.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على مصروفات تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    expenses.sort((a, b) => new Date(b.date) - new Date(a.date));

    tableBody.innerHTML = expenses.map(expense => `
        <tr>
            <td>
                <span class="expense-type-badge">${expense.type}</span>
            </td>
            <td><strong>${formatCurrency(expense.amount)}</strong></td>
            <td>${formatDate(expense.date)}</td>
            <td>${expense.beneficiary || '-'}</td>
            <td>${expense.description}</td>
            <td>
                <span class="status-badge ${getStatusClass(expense.status)}">
                    ${expense.status}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon btn-info" onclick="viewExpense(${expense.id})" title="عرض التفاصيل">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-warning" onclick="editExpense(${expense.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteExpense(${expense.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// Action functions for revenues and expenses
function viewRevenue(revenueId) {
    showAlert('سيتم تطوير عرض تفاصيل الإيراد قريباً', 'info');
}

function editRevenue(revenueId) {
    showAlert('سيتم تطوير تعديل الإيراد قريباً', 'info');
}

function deleteRevenue(revenueId) {
    if (confirm('هل أنت متأكد من حذف هذا الإيراد؟')) {
        const revenues = getCurrentYearRevenues();
        const updatedRevenues = revenues.filter(revenue => revenue.id !== revenueId);
        localStorage.setItem(`revenues_${currentFinancialYear}`, JSON.stringify(updatedRevenues));

        showAlert('تم حذف الإيراد بنجاح', 'success');
        displayFinancialOverview();
        displayRevenues();
        displayTreasuryTransactions();
    }
}

function viewExpense(expenseId) {
    showAlert('سيتم تطوير عرض تفاصيل المصروف قريباً', 'info');
}

function editExpense(expenseId) {
    showAlert('سيتم تطوير تعديل المصروف قريباً', 'info');
}

function deleteExpense(expenseId) {
    if (confirm('هل أنت متأكد من حذف هذا المصروف؟')) {
        const expenses = getCurrentYearExpenses();
        const updatedExpenses = expenses.filter(expense => expense.id !== expenseId);
        localStorage.setItem(`expenses_${currentFinancialYear}`, JSON.stringify(updatedExpenses));

        showAlert('تم حذف المصروف بنجاح', 'success');
        displayFinancialOverview();
        displayExpenses();
        displayTreasuryTransactions();
    }
}

// Quick action functions
function showRevenueBreakdown() {
    const revenues = getCurrentYearRevenues();

    if (revenues.length === 0) {
        showAlert('لا توجد إيرادات لعرض التفصيل', 'warning');
        return;
    }

    // Group revenues by type
    const revenuesByType = revenues.reduce((acc, revenue) => {
        if (!acc[revenue.type]) {
            acc[revenue.type] = 0;
        }
        acc[revenue.type] += revenue.amount;
        return acc;
    }, {});

    let breakdown = `تفصيل الإيرادات للسنة المالية ${currentFinancialYear}:\n\n`;

    Object.entries(revenuesByType).forEach(([type, amount]) => {
        breakdown += `${type}: ${formatCurrency(amount)}\n`;
    });

    const totalRevenue = Object.values(revenuesByType).reduce((sum, amount) => sum + amount, 0);
    breakdown += `\nإجمالي الإيرادات: ${formatCurrency(totalRevenue)}`;

    alert(breakdown);
}

function showExpenseBreakdown() {
    // Update year in modal title
    const expenseBreakdownYearEl = document.getElementById('expenseBreakdownYear');
    if (expenseBreakdownYearEl) {
        expenseBreakdownYearEl.textContent = currentFinancialYear;
    }

    showModal('expenseBreakdownModal');
    loadExpenseBreakdownData();
}

// Load expense breakdown data
function loadExpenseBreakdownData() {
    const expenses = getCurrentYearExpenses();

    if (expenses.length === 0) {
        document.getElementById('expenseBreakdownContent').innerHTML = `
            <div class="empty-state">
                <i class="fas fa-chart-bar fa-3x"></i>
                <h3>لا توجد مصروفات</h3>
                <p>لا توجد مصروفات مسجلة للسنة المالية ${currentFinancialYear}</p>
            </div>
        `;
        return;
    }

    // Group expenses by type
    const expensesByType = expenses.reduce((acc, expense) => {
        if (!acc[expense.type]) {
            acc[expense.type] = {
                total: 0,
                count: 0,
                items: []
            };
        }
        acc[expense.type].total += expense.amount;
        acc[expense.type].count += 1;
        acc[expense.type].items.push(expense);
        return acc;
    }, {});

    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Create breakdown content
    let content = `
        <div class="breakdown-summary">
            <h4>ملخص المصروفات للسنة المالية ${currentFinancialYear}</h4>
            <div class="summary-stats">
                <div class="stat-item">
                    <span class="label">إجمالي المصروفات:</span>
                    <span class="value">${formatCurrency(totalExpenses)}</span>
                </div>
                <div class="stat-item">
                    <span class="label">عدد المعاملات:</span>
                    <span class="value">${expenses.length}</span>
                </div>
                <div class="stat-item">
                    <span class="label">عدد الفئات:</span>
                    <span class="value">${Object.keys(expensesByType).length}</span>
                </div>
            </div>
        </div>

        <div class="breakdown-categories">
    `;

    // Sort categories by total amount (highest first)
    const sortedCategories = Object.entries(expensesByType)
        .sort(([,a], [,b]) => b.total - a.total);

    sortedCategories.forEach(([type, data]) => {
        const percentage = (data.total / totalExpenses * 100).toFixed(1);
        const averageAmount = data.total / data.count;

        content += `
            <div class="category-breakdown">
                <div class="category-header">
                    <h5>${type}</h5>
                    <div class="category-stats">
                        <span class="total">${formatCurrency(data.total)}</span>
                        <span class="percentage">${percentage}%</span>
                    </div>
                </div>
                <div class="category-details">
                    <div class="detail-stats">
                        <span>عدد المعاملات: ${data.count}</span>
                        <span>متوسط المبلغ: ${formatCurrency(averageAmount)}</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${percentage}%"></div>
                    </div>
                </div>
                <div class="category-items">
                    <button class="btn-toggle" onclick="toggleCategoryItems('${type}')">
                        <i class="fas fa-chevron-down"></i> عرض التفاصيل
                    </button>
                    <div class="items-list" id="items-${type}" style="display: none;">
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>المبلغ</th>
                                    <th>المستفيد</th>
                                    <th>الوصف</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map(item => `
                                    <tr>
                                        <td>${formatDate(item.date)}</td>
                                        <td><strong>${formatCurrency(item.amount)}</strong></td>
                                        <td>${item.beneficiary || '-'}</td>
                                        <td>${item.description}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        `;
    });

    content += '</div>';

    document.getElementById('expenseBreakdownContent').innerHTML = content;
}

// Toggle category items visibility
function toggleCategoryItems(categoryType) {
    const itemsList = document.getElementById(`items-${categoryType}`);
    const button = event.target.closest('.btn-toggle');
    const icon = button.querySelector('i');

    if (itemsList.style.display === 'none') {
        itemsList.style.display = 'block';
        icon.className = 'fas fa-chevron-up';
        button.innerHTML = '<i class="fas fa-chevron-up"></i> إخفاء التفاصيل';
    } else {
        itemsList.style.display = 'none';
        icon.className = 'fas fa-chevron-down';
        button.innerHTML = '<i class="fas fa-chevron-down"></i> عرض التفاصيل';
    }
}

function showMonthlyReport() {
    const revenues = getCurrentYearRevenues();
    const expenses = getCurrentYearExpenses();
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyRevenues = revenues.filter(revenue => {
        const revenueDate = new Date(revenue.date);
        return revenueDate.getMonth() === currentMonth && revenueDate.getFullYear() === currentYear;
    });

    const monthlyExpenses = expenses.filter(expense => {
        const expenseDate = new Date(expense.date);
        return expenseDate.getMonth() === currentMonth && expenseDate.getFullYear() === currentYear;
    });

    const monthlyRevenueTotal = monthlyRevenues.reduce((sum, revenue) => sum + revenue.amount, 0);
    const monthlyExpenseTotal = monthlyExpenses.reduce((sum, expense) => sum + expense.amount, 0);
    const monthlyBalance = monthlyRevenueTotal - monthlyExpenseTotal;

    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    let report = `التقرير الشهري - ${monthNames[currentMonth]} ${currentFinancialYear}\n\n`;
    report += `الإيرادات الشهرية: ${formatCurrency(monthlyRevenueTotal)}\n`;
    report += `المصروفات الشهرية: ${formatCurrency(monthlyExpenseTotal)}\n`;
    report += `الرصيد الشهري: ${formatCurrency(monthlyBalance)}\n\n`;
    report += `عدد الإيرادات: ${monthlyRevenues.length}\n`;
    report += `عدد المصروفات: ${monthlyExpenses.length}`;

    alert(report);
}

function showBudgetPlanning() {
    // Update year in modal title
    const budgetYearEl = document.getElementById('budgetYear');
    if (budgetYearEl) {
        budgetYearEl.textContent = currentFinancialYear;
    }

    showModal('budgetPlanningModal');
    loadBudgetData();
}

// Load budget data
function loadBudgetData() {
    const budget = JSON.parse(localStorage.getItem(`budget_${currentFinancialYear}`) || '{}');
    const revenues = getCurrentYearRevenues();
    const expenses = getCurrentYearExpenses();

    // Calculate actual totals
    const actualRevenue = revenues.reduce((sum, revenue) => sum + revenue.amount, 0);
    const actualExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    // Load planned amounts or set defaults
    const plannedRevenue = budget.plannedRevenue || 0;
    const plannedExpenses = budget.plannedExpenses || 0;

    // Update budget form
    document.getElementById('plannedRevenue').value = plannedRevenue;
    document.getElementById('plannedExpenses').value = plannedExpenses;

    // Update comparison
    updateBudgetComparison(plannedRevenue, actualRevenue, plannedExpenses, actualExpenses);

    // Load category budgets
    loadCategoryBudgets(budget);
}

// Update budget comparison
function updateBudgetComparison(plannedRevenue, actualRevenue, plannedExpenses, actualExpenses) {
    // Revenue comparison
    const revenueVariance = actualRevenue - plannedRevenue;
    const revenueVariancePercent = plannedRevenue > 0 ? (revenueVariance / plannedRevenue * 100) : 0;

    document.getElementById('actualRevenue').textContent = formatCurrency(actualRevenue);
    document.getElementById('revenueVariance').textContent = formatCurrency(revenueVariance);
    document.getElementById('revenueVariancePercent').textContent = `${revenueVariancePercent.toFixed(1)}%`;

    const revenueVarianceEl = document.getElementById('revenueVariance');
    revenueVarianceEl.className = revenueVariance >= 0 ? 'positive-variance' : 'negative-variance';

    // Expenses comparison
    const expenseVariance = actualExpenses - plannedExpenses;
    const expenseVariancePercent = plannedExpenses > 0 ? (expenseVariance / plannedExpenses * 100) : 0;

    document.getElementById('actualExpenses').textContent = formatCurrency(actualExpenses);
    document.getElementById('expenseVariance').textContent = formatCurrency(expenseVariance);
    document.getElementById('expenseVariancePercent').textContent = `${expenseVariancePercent.toFixed(1)}%`;

    const expenseVarianceEl = document.getElementById('expenseVariance');
    expenseVarianceEl.className = expenseVariance <= 0 ? 'positive-variance' : 'negative-variance';

    // Net budget
    const plannedNet = plannedRevenue - plannedExpenses;
    const actualNet = actualRevenue - actualExpenses;
    const netVariance = actualNet - plannedNet;

    document.getElementById('plannedNet').textContent = formatCurrency(plannedNet);
    document.getElementById('actualNet').textContent = formatCurrency(actualNet);
    document.getElementById('netVariance').textContent = formatCurrency(netVariance);

    const netVarianceEl = document.getElementById('netVariance');
    netVarianceEl.className = netVariance >= 0 ? 'positive-variance' : 'negative-variance';
}

// Load category budgets
function loadCategoryBudgets(budget) {
    const categoryBudgets = budget.categoryBudgets || {};

    // Revenue categories
    const revenueCategories = [
        'اشتراكات الموظفين',
        'إيرادات المطعم',
        'إيرادات الرحلات',
        'إيرادات الفعاليات',
        'تبرعات',
        'أخرى'
    ];

    // Expense categories
    const expenseCategories = [
        'مصروفات المطعم',
        'مصروفات الرحلات',
        'مصروفات الفعاليات',
        'إعانات صحية',
        'قروض وسلفيات',
        'مصروفات إدارية',
        'أخرى'
    ];

    // Load revenue category budgets
    const revenueCategoriesContainer = document.getElementById('revenueCategoriesBudget');
    if (revenueCategoriesContainer) {
        revenueCategoriesContainer.innerHTML = revenueCategories.map(category => {
            const planned = categoryBudgets[`revenue_${category}`] || 0;
            const actual = getCurrentYearRevenues()
                .filter(revenue => revenue.type === category)
                .reduce((sum, revenue) => sum + revenue.amount, 0);
            const variance = actual - planned;
            const variancePercent = planned > 0 ? (variance / planned * 100) : 0;

            return `
                <div class="category-budget-item">
                    <div class="category-info">
                        <label>${category}</label>
                        <div class="category-amounts">
                            <span class="planned">مخطط: ${formatCurrency(planned)}</span>
                            <span class="actual">فعلي: ${formatCurrency(actual)}</span>
                            <span class="variance ${variance >= 0 ? 'positive' : 'negative'}">
                                الفرق: ${formatCurrency(variance)} (${variancePercent.toFixed(1)}%)
                            </span>
                        </div>
                    </div>
                    <input type="number"
                           id="revenue_${category}"
                           value="${planned}"
                           min="0"
                           step="1000"
                           placeholder="المبلغ المخطط">
                </div>
            `;
        }).join('');
    }

    // Load expense category budgets
    const expenseCategoriesContainer = document.getElementById('expenseCategoriesBudget');
    if (expenseCategoriesContainer) {
        expenseCategoriesContainer.innerHTML = expenseCategories.map(category => {
            const planned = categoryBudgets[`expense_${category}`] || 0;
            const actual = getCurrentYearExpenses()
                .filter(expense => expense.type === category)
                .reduce((sum, expense) => sum + expense.amount, 0);
            const variance = actual - planned;
            const variancePercent = planned > 0 ? (variance / planned * 100) : 0;

            return `
                <div class="category-budget-item">
                    <div class="category-info">
                        <label>${category}</label>
                        <div class="category-amounts">
                            <span class="planned">مخطط: ${formatCurrency(planned)}</span>
                            <span class="actual">فعلي: ${formatCurrency(actual)}</span>
                            <span class="variance ${variance <= 0 ? 'positive' : 'negative'}">
                                الفرق: ${formatCurrency(variance)} (${variancePercent.toFixed(1)}%)
                            </span>
                        </div>
                    </div>
                    <input type="number"
                           id="expense_${category}"
                           value="${planned}"
                           min="0"
                           step="1000"
                           placeholder="المبلغ المخطط">
                </div>
            `;
        }).join('');
    }
}

// Save budget planning
function saveBudgetPlanning() {
    const plannedRevenue = parseFloat(document.getElementById('plannedRevenue').value) || 0;
    const plannedExpenses = parseFloat(document.getElementById('plannedExpenses').value) || 0;

    if (plannedRevenue < 0 || plannedExpenses < 0) {
        showAlert('المبالغ يجب أن تكون أكبر من أو تساوي صفر', 'error');
        return;
    }

    // Collect category budgets
    const categoryBudgets = {};

    // Revenue categories
    const revenueCategories = [
        'اشتراكات الموظفين',
        'إيرادات المطعم',
        'إيرادات الرحلات',
        'إيرادات الفعاليات',
        'تبرعات',
        'أخرى'
    ];

    // Expense categories
    const expenseCategories = [
        'مصروفات المطعم',
        'مصروفات الرحلات',
        'مصروفات الفعاليات',
        'إعانات صحية',
        'قروض وسلفيات',
        'مصروفات إدارية',
        'أخرى'
    ];

    // Collect revenue category budgets
    revenueCategories.forEach(category => {
        const input = document.getElementById(`revenue_${category}`);
        if (input) {
            categoryBudgets[`revenue_${category}`] = parseFloat(input.value) || 0;
        }
    });

    // Collect expense category budgets
    expenseCategories.forEach(category => {
        const input = document.getElementById(`expense_${category}`);
        if (input) {
            categoryBudgets[`expense_${category}`] = parseFloat(input.value) || 0;
        }
    });

    // Validate category totals
    const totalRevenueBudget = Object.keys(categoryBudgets)
        .filter(key => key.startsWith('revenue_'))
        .reduce((sum, key) => sum + categoryBudgets[key], 0);

    const totalExpenseBudget = Object.keys(categoryBudgets)
        .filter(key => key.startsWith('expense_'))
        .reduce((sum, key) => sum + categoryBudgets[key], 0);

    if (totalRevenueBudget > plannedRevenue) {
        showAlert(`مجموع ميزانيات فئات الإيرادات (${formatCurrency(totalRevenueBudget)}) أكبر من إجمالي الإيرادات المخططة (${formatCurrency(plannedRevenue)})`, 'warning');
    }

    if (totalExpenseBudget > plannedExpenses) {
        showAlert(`مجموع ميزانيات فئات المصروفات (${formatCurrency(totalExpenseBudget)}) أكبر من إجمالي المصروفات المخططة (${formatCurrency(plannedExpenses)})`, 'warning');
    }

    // Create budget object
    const budget = {
        plannedRevenue: plannedRevenue,
        plannedExpenses: plannedExpenses,
        categoryBudgets: categoryBudgets,
        financialYear: currentFinancialYear,
        lastUpdated: new Date().toISOString(),
        createdBy: 'النظام' // يمكن تحديثه لاحقاً لإضافة معلومات المستخدم
    };

    // Save budget
    localStorage.setItem(`budget_${currentFinancialYear}`, JSON.stringify(budget));

    showAlert(`تم حفظ تخطيط الميزانية للسنة المالية ${currentFinancialYear} بنجاح`, 'success');
    hideModal('budgetPlanningModal');

    // Refresh budget data
    loadBudgetData();
}

// Export budget report
function exportBudgetReport() {
    const budget = JSON.parse(localStorage.getItem(`budget_${currentFinancialYear}`) || '{}');
    const revenues = getCurrentYearRevenues();
    const expenses = getCurrentYearExpenses();

    if (Object.keys(budget).length === 0) {
        showAlert('لا توجد ميزانية مخططة لتصديرها', 'warning');
        return;
    }

    const actualRevenue = revenues.reduce((sum, revenue) => sum + revenue.amount, 0);
    const actualExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    const plannedRevenue = budget.plannedRevenue || 0;
    const plannedExpenses = budget.plannedExpenses || 0;

    const revenueVariance = actualRevenue - plannedRevenue;
    const expenseVariance = actualExpenses - plannedExpenses;
    const netVariance = (actualRevenue - actualExpenses) - (plannedRevenue - plannedExpenses);

    let reportContent = `تقرير تخطيط الميزانية - السنة المالية ${currentFinancialYear}
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}
آخر تحديث للميزانية: ${budget.lastUpdated ? new Date(budget.lastUpdated).toLocaleDateString('ar-DZ') : '-'}

ملخص الميزانية:
- الإيرادات المخططة: ${formatCurrency(plannedRevenue)}
- الإيرادات الفعلية: ${formatCurrency(actualRevenue)}
- انحراف الإيرادات: ${formatCurrency(revenueVariance)} (${plannedRevenue > 0 ? (revenueVariance / plannedRevenue * 100).toFixed(1) : 0}%)

- المصروفات المخططة: ${formatCurrency(plannedExpenses)}
- المصروفات الفعلية: ${formatCurrency(actualExpenses)}
- انحراف المصروفات: ${formatCurrency(expenseVariance)} (${plannedExpenses > 0 ? (expenseVariance / plannedExpenses * 100).toFixed(1) : 0}%)

- صافي الميزانية المخططة: ${formatCurrency(plannedRevenue - plannedExpenses)}
- صافي الميزانية الفعلية: ${formatCurrency(actualRevenue - actualExpenses)}
- انحراف صافي الميزانية: ${formatCurrency(netVariance)}

تفصيل ميزانيات الفئات:

الإيرادات:
`;

    const categoryBudgets = budget.categoryBudgets || {};

    // Revenue categories
    const revenueCategories = [
        'اشتراكات الموظفين',
        'إيرادات المطعم',
        'إيرادات الرحلات',
        'إيرادات الفعاليات',
        'تبرعات',
        'أخرى'
    ];

    revenueCategories.forEach(category => {
        const planned = categoryBudgets[`revenue_${category}`] || 0;
        const actual = revenues
            .filter(revenue => revenue.type === category)
            .reduce((sum, revenue) => sum + revenue.amount, 0);
        const variance = actual - planned;
        const variancePercent = planned > 0 ? (variance / planned * 100).toFixed(1) : 0;

        reportContent += `- ${category}: مخطط ${formatCurrency(planned)}, فعلي ${formatCurrency(actual)}, الفرق ${formatCurrency(variance)} (${variancePercent}%)\n`;
    });

    reportContent += '\nالمصروفات:\n';

    // Expense categories
    const expenseCategories = [
        'مصروفات المطعم',
        'مصروفات الرحلات',
        'مصروفات الفعاليات',
        'إعانات صحية',
        'قروض وسلفيات',
        'مصروفات إدارية',
        'أخرى'
    ];

    expenseCategories.forEach(category => {
        const planned = categoryBudgets[`expense_${category}`] || 0;
        const actual = expenses
            .filter(expense => expense.type === category)
            .reduce((sum, expense) => sum + expense.amount, 0);
        const variance = actual - planned;
        const variancePercent = planned > 0 ? (variance / planned * 100).toFixed(1) : 0;

        reportContent += `- ${category}: مخطط ${formatCurrency(planned)}, فعلي ${formatCurrency(actual)}, الفرق ${formatCurrency(variance)} (${variancePercent}%)\n`;
    });

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تقرير_الميزانية_${currentFinancialYear}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert(`تم تصدير تقرير الميزانية للسنة ${currentFinancialYear} بنجاح`, 'success');
}

// Export expense breakdown report
function exportExpenseBreakdownReport() {
    const expenses = getCurrentYearExpenses();

    if (expenses.length === 0) {
        showAlert('لا توجد مصروفات لتصدير تفصيلها', 'warning');
        return;
    }

    // Group expenses by type
    const expensesByType = expenses.reduce((acc, expense) => {
        if (!acc[expense.type]) {
            acc[expense.type] = {
                total: 0,
                count: 0,
                items: []
            };
        }
        acc[expense.type].total += expense.amount;
        acc[expense.type].count += 1;
        acc[expense.type].items.push(expense);
        return acc;
    }, {});

    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);

    let reportContent = `تقرير تفصيل المصروفات - السنة المالية ${currentFinancialYear}
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

ملخص المصروفات:
- إجمالي المصروفات: ${formatCurrency(totalExpenses)}
- عدد المعاملات: ${expenses.length}
- عدد الفئات: ${Object.keys(expensesByType).length}

تفصيل المصروفات حسب الفئة:

`;

    // Sort categories by total amount (highest first)
    const sortedCategories = Object.entries(expensesByType)
        .sort(([,a], [,b]) => b.total - a.total);

    sortedCategories.forEach(([type, data], index) => {
        const percentage = (data.total / totalExpenses * 100).toFixed(1);
        const averageAmount = data.total / data.count;

        reportContent += `${index + 1}. ${type}
   إجمالي المبلغ: ${formatCurrency(data.total)} (${percentage}%)
   عدد المعاملات: ${data.count}
   متوسط المبلغ: ${formatCurrency(averageAmount)}

   تفاصيل المعاملات:
`;

        // Sort items by date (newest first)
        data.items.sort((a, b) => new Date(b.date) - new Date(a.date));

        data.items.forEach((item, itemIndex) => {
            reportContent += `   ${itemIndex + 1}. التاريخ: ${formatDate(item.date)}
      المبلغ: ${formatCurrency(item.amount)}
      المستفيد: ${item.beneficiary || '-'}
      الوصف: ${item.description}
      الحالة: ${item.status}
      ${item.notes ? `ملاحظات: ${item.notes}` : ''}

`;
        });

        reportContent += '\n';
    });

    // Add monthly breakdown
    reportContent += 'التوزيع الشهري للمصروفات:\n\n';

    const monthlyExpenses = {};
    const monthNames = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];

    expenses.forEach(expense => {
        const date = new Date(expense.date);
        const monthKey = `${date.getFullYear()}-${date.getMonth()}`;
        const monthName = `${monthNames[date.getMonth()]} ${date.getFullYear()}`;

        if (!monthlyExpenses[monthKey]) {
            monthlyExpenses[monthKey] = {
                name: monthName,
                total: 0,
                count: 0
            };
        }

        monthlyExpenses[monthKey].total += expense.amount;
        monthlyExpenses[monthKey].count += 1;
    });

    // Sort months chronologically
    const sortedMonths = Object.entries(monthlyExpenses)
        .sort(([a], [b]) => a.localeCompare(b));

    sortedMonths.forEach(([key, data]) => {
        const percentage = (data.total / totalExpenses * 100).toFixed(1);
        reportContent += `${data.name}: ${formatCurrency(data.total)} (${percentage}%) - ${data.count} معاملة\n`;
    });

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `تفصيل_المصروفات_${currentFinancialYear}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert(`تم تصدير تقرير تفصيل المصروفات للسنة ${currentFinancialYear} بنجاح`, 'success');
}

// Loans Management Functions

// Display loans
async function displayLoans() {
    try {
        const loans = getCurrentYearLoans();
        const payments = getCurrentYearLoanPayments();
        const employees = await db.getEmployees();
        const tableBody = document.getElementById('loansTableBody');

    if (!tableBody) return;

    if (loans.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-hand-holding-usd fa-2x"></i>
                        <h3>لا توجد قروض مسجلة</h3>
                        <p>اضغط على "إضافة قرض جديد" لبدء إضافة القروض</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    loans.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));

    tableBody.innerHTML = loans.map(loan => {
        const employee = employees.find(emp => emp.id === loan.employeeId);
        const employeeName = employee ? employee.name : 'غير محدد';

        // Calculate paid amount from payments
        const loanPayments = payments.filter(payment => payment.loanId === loan.id);
        const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingAmount = loan.amount - paidAmount;

        // Update loan status based on remaining amount
        let status = loan.status;
        if (remainingAmount <= 0) {
            status = 'مسدد';
        } else if (status !== 'ملغي') {
            status = 'نشط';
        }

        return `
            <tr>
                <td><strong>${employeeName}</strong></td>
                <td>
                    <span class="loan-type-badge">${loan.type}</span>
                </td>
                <td><strong>${formatCurrency(loan.amount)}</strong></td>
                <td><span class="paid-amount">${formatCurrency(paidAmount)}</span></td>
                <td><span class="remaining-amount">${formatCurrency(remainingAmount)}</span></td>
                <td><strong>${formatCurrency(loan.monthlyInstallment)}</strong></td>
                <td>${formatDate(loan.startDate)}</td>
                <td>
                    <span class="status-badge ${getLoanStatusClass(status)}">
                        ${status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewLoanDetails(${loan.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${status === 'نشط' && remainingAmount > 0 ? `
                            <button class="btn-icon btn-success" onclick="showPayInstallmentModal(${loan.id})" title="دفع قسط">
                                <i class="fas fa-money-bill"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-warning" onclick="editLoan(${loan.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteLoan(${loan.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
    } catch (error) {
        console.error('Error displaying loans:', error);
        const tableBody = document.getElementById('loansTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                            <h3>خطأ في تحميل البيانات</h3>
                            <p>حدث خطأ أثناء تحميل قائمة القروض</p>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
}

// Get loan status class for styling
function getLoanStatusClass(status) {
    switch(status) {
        case 'نشط': return 'active';
        case 'مسدد': return 'completed';
        case 'متأخر': return 'overdue';
        case 'ملغي': return 'cancelled';
        default: return '';
    }
}

// Check if employee has active loan
function hasActiveLoan(employeeId) {
    const loans = getCurrentYearLoans();
    const payments = getCurrentYearLoanPayments();

    return loans.some(loan => {
        if (loan.employeeId !== parseInt(employeeId)) return false;
        if (loan.status === 'ملغي') return false;

        // Calculate remaining amount
        const loanPayments = payments.filter(payment => payment.loanId === loan.id);
        const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingAmount = loan.amount - paidAmount;

        return remainingAmount > 0;
    });
}

// Calculate monthly installment
function calculateMonthlyInstallment() {
    const amount = parseFloat(document.getElementById('loanAmount').value) || 0;
    const installments = parseInt(document.getElementById('loanInstallments').value) || 1;

    if (amount > 0 && installments > 0) {
        const monthlyInstallment = amount / installments;
        document.getElementById('monthlyInstallment').value = Math.round(monthlyInstallment);
    } else {
        document.getElementById('monthlyInstallment').value = '';
    }
}

// Save loan
async function saveLoan() {
    try {
    const employeeId = document.getElementById('loanEmployee').value;
    const type = document.getElementById('loanType').value;
    const amount = parseFloat(document.getElementById('loanAmount').value) || 0;
    const installments = parseInt(document.getElementById('loanInstallments').value) || 0;
    const startDate = document.getElementById('loanStartDate').value;
    const monthlyInstallment = parseFloat(document.getElementById('monthlyInstallment').value) || 0;
    const purpose = document.getElementById('loanPurpose').value.trim();
    const notes = document.getElementById('loanNotes').value.trim();

    // Validation
    if (!employeeId || !type || !amount || !installments || !startDate || !purpose) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    if (amount < 1000) {
        showAlert('المبلغ يجب أن يكون أكبر من 1000 دج', 'error');
        return;
    }

    if (installments < 1 || installments > 60) {
        showAlert('عدد الأقساط يجب أن يكون بين 1 و 60', 'error');
        return;
    }

    // Check if employee has active loan
    const hasActiveLoans = await db.employeeHasActiveLoans(employeeId);
    if (hasActiveLoans) {
        const employees = await db.getEmployees();
        const employee = employees.find(emp => emp.id == employeeId);
        const employeeName = employee ? employee.name : 'الموظف';
        showAlert(`${employeeName} لديه قرض جاري التسديد. لا يمكن منح قرض جديد حتى يتم تسديد القرض الحالي بالكامل.`, 'error');
        return;
    }

    // Get employee info
    const employees = await db.getEmployees();
    const employee = employees.find(emp => emp.id == employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // Create loan object
    const loan = {
        id: Date.now(),
        employeeId: parseInt(employeeId),
        employeeName: employee.name,
        type: type,
        amount: amount,
        installments: installments,
        monthlyInstallment: monthlyInstallment,
        startDate: startDate,
        purpose: purpose,
        notes: notes,
        status: 'نشط',
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    // Save loan for current financial year
    const loans = getCurrentYearLoans();
    loans.push(loan);
    localStorage.setItem(`loans_${currentFinancialYear}`, JSON.stringify(loans));

    // Add to expenses (loan disbursement)
    const expense = {
        id: Date.now() + 1,
        type: 'قروض وسلفيات',
        amount: amount,
        date: startDate,
        beneficiary: employee.name,
        status: 'مدفوع',
        category: 'ضروري',
        description: `قرض ${type} للموظف ${employee.name}`,
        notes: purpose,
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    const expenses = getCurrentYearExpenses();
    expenses.push(expense);
    localStorage.setItem(`expenses_${currentFinancialYear}`, JSON.stringify(expenses));

    // Add to treasury transactions
    addTreasuryTransaction('مصروف', -amount, `قرض ${type}: ${employee.name}`, startDate);

    showAlert(`تم حفظ القرض بنجاح للموظف ${employee.name}`, 'success');
    hideModal('addLoanModal');
    resetLoanForm();
    displayFinancialOverview();
    displayLoans();
    displayExpenses();
    displayTreasuryTransactions();
    } catch (error) {
        console.error('Error saving loan:', error);
        showAlert('حدث خطأ أثناء حفظ القرض: ' + error.message, 'error');
    }
}

// Reset loan form
function resetLoanForm() {
    document.getElementById('addLoanForm').reset();
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('loanStartDate').value = today;
}

// Show pay installment modal
async function showPayInstallmentModal(loanId) {
    try {
        const loans = getCurrentYearLoans();
        const payments = getCurrentYearLoanPayments();
        const loan = loans.find(l => l.id === loanId);

        if (!loan) {
            showAlert('لم يتم العثور على القرض', 'error');
            return;
        }

        const employees = await db.getEmployees();
        const employee = employees.find(emp => emp.id === loan.employeeId);
    const loanPayments = payments.filter(payment => payment.loanId === loanId);
    const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = loan.amount - paidAmount;

    if (remainingAmount <= 0) {
        showAlert('هذا القرض مسدد بالكامل', 'info');
        return;
    }

    // Fill payment modal
    document.getElementById('paymentLoanId').value = loanId;
    document.getElementById('paymentEmployeeName').textContent = employee ? employee.name : 'غير محدد';
    document.getElementById('paymentLoanType').textContent = loan.type;
    document.getElementById('paymentTotalAmount').textContent = formatCurrency(loan.amount);
    document.getElementById('paymentPaidAmount').textContent = formatCurrency(paidAmount);
    document.getElementById('paymentRemainingAmount').textContent = formatCurrency(remainingAmount);
    document.getElementById('paymentMonthlyInstallment').textContent = formatCurrency(loan.monthlyInstallment);

    // Set suggested payment amount (monthly installment or remaining amount, whichever is smaller)
    const suggestedAmount = Math.min(loan.monthlyInstallment, remainingAmount);
    document.getElementById('paymentInstallmentAmount').value = suggestedAmount;

    // Set current date
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('paymentDate').value = today;

    showModal('payInstallmentModal');
    } catch (error) {
        console.error('Error showing payment modal:', error);
        showAlert('حدث خطأ أثناء تحميل معلومات القرض', 'error');
    }
}

// Save installment payment
async function saveInstallmentPayment() {
    try {
    const loanId = parseInt(document.getElementById('paymentLoanId').value);
    const paymentAmount = parseFloat(document.getElementById('paymentInstallmentAmount').value) || 0;
    const paymentDate = document.getElementById('paymentDate').value;
    const paymentNotes = document.getElementById('paymentNotes').value.trim();

    if (!paymentAmount || paymentAmount <= 0) {
        showAlert('يرجى إدخال مبلغ صحيح', 'error');
        return;
    }

    if (!paymentDate) {
        showAlert('يرجى تحديد تاريخ الدفع', 'error');
        return;
    }

    // Get loan info
    const loans = getCurrentYearLoans();
    const loan = loans.find(l => l.id === loanId);

    if (!loan) {
        showAlert('لم يتم العثور على القرض', 'error');
        return;
    }

    // Calculate current remaining amount
    const payments = getCurrentYearLoanPayments();
    const loanPayments = payments.filter(payment => payment.loanId === loanId);
    const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
    const remainingAmount = loan.amount - paidAmount;

    if (paymentAmount > remainingAmount) {
        showAlert(`مبلغ الدفع (${formatCurrency(paymentAmount)}) أكبر من المبلغ المتبقي (${formatCurrency(remainingAmount)})`, 'error');
        return;
    }

    // Create payment object
    const payment = {
        id: Date.now(),
        loanId: loanId,
        amount: paymentAmount,
        date: paymentDate,
        notes: paymentNotes,
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    // Save payment
    payments.push(payment);
    localStorage.setItem(`loanPayments_${currentFinancialYear}`, JSON.stringify(payments));

    // Add to revenues (loan repayment)
    const employees = await db.getEmployees();
    const employee = employees.find(emp => emp.id === loan.employeeId);
    const revenue = {
        id: Date.now() + 1,
        source: `تسديد قسط قرض - ${employee ? employee.name : 'غير محدد'}`,
        type: 'أخرى',
        amount: paymentAmount,
        date: paymentDate,
        status: 'مستلم',
        category: 'استثنائي',
        description: `تسديد قسط من قرض ${loan.type}`,
        notes: paymentNotes,
        financialYear: currentFinancialYear,
        createdAt: new Date().toISOString()
    };

    const revenues = getCurrentYearRevenues();
    revenues.push(revenue);
    localStorage.setItem(`revenues_${currentFinancialYear}`, JSON.stringify(revenues));

    // Add to treasury transactions
    addTreasuryTransaction('إيراد', paymentAmount, `تسديد قسط قرض: ${employee ? employee.name : 'غير محدد'}`, paymentDate);

    showAlert('تم تسجيل دفع القسط بنجاح', 'success');
    hideModal('payInstallmentModal');
    resetPaymentForm();
    displayFinancialOverview();
    displayLoans();
    displayRevenues();
    displayTreasuryTransactions();
    } catch (error) {
        console.error('Error saving installment payment:', error);
        showAlert('حدث خطأ أثناء حفظ دفع القسط: ' + error.message, 'error');
    }
}

// Reset payment form
function resetPaymentForm() {
    document.getElementById('payInstallmentForm').reset();
}

// Search loans
async function searchLoans() {
    try {
        const searchTerm = document.getElementById('loanSearch').value.toLowerCase();
        const loans = getCurrentYearLoans();
        const employees = await db.getEmployees();

    const filteredLoans = loans.filter(loan => {
        const employee = employees.find(emp => emp.id === loan.employeeId);
        const employeeName = employee ? employee.name.toLowerCase() : '';

        return employeeName.includes(searchTerm) ||
               loan.type.toLowerCase().includes(searchTerm) ||
               loan.purpose.toLowerCase().includes(searchTerm);
    });

    displayFilteredLoans(filteredLoans);
    } catch (error) {
        console.error('Error searching loans:', error);
        showAlert('حدث خطأ أثناء البحث في القروض', 'error');
    }
}

// Filter loans
function filterLoans() {
    const filterValue = document.getElementById('loanFilter').value;
    const loans = getCurrentYearLoans();
    const payments = getCurrentYearLoanPayments();

    let filteredLoans = loans;

    if (filterValue) {
        filteredLoans = loans.filter(loan => {
            // Calculate current status
            const loanPayments = payments.filter(payment => payment.loanId === loan.id);
            const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
            const remainingAmount = loan.amount - paidAmount;

            let currentStatus = loan.status;
            if (remainingAmount <= 0) {
                currentStatus = 'مسدد';
            } else if (loan.status !== 'ملغي') {
                currentStatus = 'نشط';
            }

            return currentStatus === filterValue;
        });
    }

    displayFilteredLoans(filteredLoans);
}

// Display filtered loans
async function displayFilteredLoans(loans) {
    try {
        const payments = getCurrentYearLoanPayments();
        const employees = await db.getEmployees();
        const tableBody = document.getElementById('loansTableBody');

    if (!tableBody) return;

    if (loans.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-2x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على قروض تطابق معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // Sort by date (newest first)
    loans.sort((a, b) => new Date(b.startDate) - new Date(a.startDate));

    tableBody.innerHTML = loans.map(loan => {
        const employee = employees.find(emp => emp.id === loan.employeeId);
        const employeeName = employee ? employee.name : 'غير محدد';

        // Calculate paid amount from payments
        const loanPayments = payments.filter(payment => payment.loanId === loan.id);
        const paidAmount = loanPayments.reduce((sum, payment) => sum + payment.amount, 0);
        const remainingAmount = loan.amount - paidAmount;

        // Update loan status based on remaining amount
        let status = loan.status;
        if (remainingAmount <= 0) {
            status = 'مسدد';
        } else if (status !== 'ملغي') {
            status = 'نشط';
        }

        return `
            <tr>
                <td><strong>${employeeName}</strong></td>
                <td>
                    <span class="loan-type-badge">${loan.type}</span>
                </td>
                <td><strong>${formatCurrency(loan.amount)}</strong></td>
                <td><span class="paid-amount">${formatCurrency(paidAmount)}</span></td>
                <td><span class="remaining-amount">${formatCurrency(remainingAmount)}</span></td>
                <td><strong>${formatCurrency(loan.monthlyInstallment)}</strong></td>
                <td>${formatDate(loan.startDate)}</td>
                <td>
                    <span class="status-badge ${getLoanStatusClass(status)}">
                        ${status}
                    </span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn-icon btn-info" onclick="viewLoanDetails(${loan.id})" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        ${status === 'نشط' && remainingAmount > 0 ? `
                            <button class="btn-icon btn-success" onclick="showPayInstallmentModal(${loan.id})" title="دفع قسط">
                                <i class="fas fa-money-bill"></i>
                            </button>
                        ` : ''}
                        <button class="btn-icon btn-warning" onclick="editLoan(${loan.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn-icon btn-danger" onclick="deleteLoan(${loan.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `;
    }).join('');
    } catch (error) {
        console.error('Error displaying filtered loans:', error);
        const tableBody = document.getElementById('loansTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="9" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                            <h3>خطأ في تحميل البيانات</h3>
                            <p>حدث خطأ أثناء عرض القروض المفلترة</p>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
}

// Other loan functions (placeholders for future development)
function viewLoanDetails(loanId) {
    showAlert('سيتم تطوير عرض تفاصيل القرض قريباً', 'info');
}

function editLoan(loanId) {
    showAlert('سيتم تطوير تعديل القرض قريباً', 'info');
}

function deleteLoan(loanId) {
    if (confirm('هل أنت متأكد من حذف هذا القرض؟ سيتم حذف جميع الدفعات المرتبطة به أيضاً.')) {
        // Delete loan
        const loans = getCurrentYearLoans();
        const updatedLoans = loans.filter(loan => loan.id !== loanId);
        localStorage.setItem(`loans_${currentFinancialYear}`, JSON.stringify(updatedLoans));

        // Delete related payments
        const payments = getCurrentYearLoanPayments();
        const updatedPayments = payments.filter(payment => payment.loanId !== loanId);
        localStorage.setItem(`loanPayments_${currentFinancialYear}`, JSON.stringify(updatedPayments));

        showAlert('تم حذف القرض بنجاح', 'success');
        displayFinancialOverview();
        displayLoans();
        displayRevenues();
        displayExpenses();
        displayTreasuryTransactions();
    }
}

// Export financial report
function exportFinancialReport() {
    const revenues = getCurrentYearRevenues();
    const expenses = getCurrentYearExpenses();
    const openingBalance = getCurrentYearOpeningBalance();

    if (revenues.length === 0 && expenses.length === 0) {
        showAlert('لا توجد بيانات مالية لتصديرها', 'warning');
        return;
    }

    const totalRevenue = revenues.reduce((sum, revenue) => sum + revenue.amount, 0);
    const totalExpenses = expenses.reduce((sum, expense) => sum + expense.amount, 0);
    const currentBalance = openingBalance + totalRevenue - totalExpenses;

    let reportContent = `التقرير المالي الشامل - السنة المالية ${currentFinancialYear}
التاريخ: ${new Date().toLocaleDateString('ar-DZ')}

الملخص المالي:
- الرصيد الافتتاحي: ${formatCurrency(openingBalance)}
- إجمالي الإيرادات: ${formatCurrency(totalRevenue)}
- إجمالي المصروفات: ${formatCurrency(totalExpenses)}
- الرصيد الحالي: ${formatCurrency(currentBalance)}

تفصيل الإيرادات:
`;

    if (revenues.length > 0) {
        revenues.forEach((revenue, index) => {
            reportContent += `
${index + 1}. ${revenue.source}
   النوع: ${revenue.type}
   المبلغ: ${formatCurrency(revenue.amount)}
   التاريخ: ${formatDate(revenue.date)}
   الحالة: ${revenue.status}
   الوصف: ${revenue.description}
`;
        });
    } else {
        reportContent += '\nلا توجد إيرادات مسجلة\n';
    }

    reportContent += '\nتفصيل المصروفات:\n';

    if (expenses.length > 0) {
        expenses.forEach((expense, index) => {
            reportContent += `
${index + 1}. ${expense.type}
   المبلغ: ${formatCurrency(expense.amount)}
   التاريخ: ${formatDate(expense.date)}
   المستفيد: ${expense.beneficiary || '-'}
   الحالة: ${expense.status}
   الوصف: ${expense.description}
`;
        });
    } else {
        reportContent += '\nلا توجد مصروفات مسجلة\n';
    }

    // Export file
    const blob = new Blob([reportContent], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `التقرير_المالي_${currentFinancialYear}_${new Date().toISOString().split('T')[0]}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    showAlert(`تم تصدير التقرير المالي للسنة ${currentFinancialYear} بنجاح`, 'success');
}
