// وظائف إدارة الموظفين
document.addEventListener('DOMContentLoaded', function() {
    initializeEmployeesPage();
});

// تهيئة صفحة الموظفين
function initializeEmployeesPage() {
    updateCurrentDate();
    loadWorkplaceOptions();
    loadBankBranchOptions();
    loadEmployeesData();
    loadEmployeeStatistics();
    setupSearchAndFilter();
}

// تحميل بيانات الموظفين
async function loadEmployeesData() {
    try {
        const employees = await db.getEmployees();
        const tableBody = document.getElementById('employeesTableBody');

    if (!tableBody) return;

    if (employees.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-users fa-3x"></i>
                        <h3>لا يوجد موظفين مسجلين</h3>
                        <p>ابدأ بإضافة موظف جديد</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    tableBody.innerHTML = employees.map(employee => `
        <tr>
            <td>
                <div class="employee-photo">
                    ${employee.photo ?
                        `<img src="${employee.photo}" alt="${employee.name}">` :
                        `<div class="photo-placeholder"><i class="fas fa-user"></i></div>`
                    }
                </div>
            </td>
            <td><strong>${employee.name}</strong></td>
            <td>${employee.position}</td>
            <td>${employee.department}</td>
            <td>
                <span class="gender-badge ${employee.gender === 'ذكر' ? 'male' : 'female'}">
                    <i class="fas fa-${employee.gender === 'ذكر' ? 'mars' : 'venus'}"></i>
                    ${employee.gender}
                </span>
            </td>
            <td>${db.formatDate(employee.hireDate)}</td>
            <td><strong>${db.formatCurrency(employee.salary)}</strong></td>
            <td>
                <span class="children-count">
                    <i class="fas fa-child"></i>
                    ${employee.children ? employee.children.length : 0}
                </span>
            </td>
            <td>
                <span class="status-badge ${employee.status === 'نشط' ? 'active' : 'inactive'}">
                    ${employee.status || 'نشط'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon btn-primary" onclick="viewEmployee(${employee.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-warning" onclick="editEmployee(${employee.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteEmployee(${employee.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
    } catch (error) {
        console.error('Error loading employees data:', error);
        const tableBody = document.getElementById('employeesTableBody');
        if (tableBody) {
            tableBody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="empty-state">
                            <i class="fas fa-exclamation-triangle fa-3x"></i>
                            <h3>خطأ في تحميل البيانات</h3>
                            <p>حدث خطأ أثناء تحميل بيانات الموظفين</p>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
}

// تحميل إحصائيات الموظفين
async function loadEmployeeStatistics() {
    try {
        const employees = await db.getEmployees();
    const stats = {
        total: employees.length,
        male: employees.filter(emp => emp.gender === 'ذكر').length,
        female: employees.filter(emp => emp.gender === 'أنثى').length,
        totalChildren: employees.reduce((sum, emp) => sum + (emp.children ? emp.children.length : 0), 0)
    };

    updateElement('totalEmployeesCount', stats.total);
    updateElement('maleEmployeesCount', stats.male);
    updateElement('femaleEmployeesCount', stats.female);
    updateElement('totalChildrenCount', stats.totalChildren);
    } catch (error) {
        console.error('Error loading employee statistics:', error);
        // Set default values in case of error
        updateElement('totalEmployeesCount', 0);
        updateElement('maleEmployeesCount', 0);
        updateElement('femaleEmployeesCount', 0);
        updateElement('totalChildrenCount', 0);
    }
}

// إعداد البحث والفلترة
function setupSearchAndFilter() {
    const searchInput = document.getElementById('employeeSearch');
    const departmentFilter = document.getElementById('departmentFilter');
    const genderFilter = document.getElementById('genderFilter');

    if (searchInput) {
        searchInput.addEventListener('input', filterEmployees);
    }

    if (departmentFilter) {
        departmentFilter.addEventListener('change', filterEmployees);
    }

    if (genderFilter) {
        genderFilter.addEventListener('change', filterEmployees);
    }
}

// فلترة الموظفين
async function filterEmployees() {
    try {
        const searchTerm = document.getElementById('employeeSearch').value.toLowerCase();
        const workplaceFilter = document.getElementById('departmentFilter').value;
        const genderFilter = document.getElementById('genderFilter').value;

        const employees = await db.getEmployees();
    const filteredEmployees = employees.filter(employee => {
        const matchesSearch = employee.name.toLowerCase().includes(searchTerm) ||
                            employee.position.toLowerCase().includes(searchTerm) ||
                            employee.department.toLowerCase().includes(searchTerm);

        const matchesWorkplace = !workplaceFilter || employee.department === workplaceFilter;
        const matchesGender = !genderFilter || employee.gender === genderFilter;

        return matchesSearch && matchesWorkplace && matchesGender;
    });

    displayFilteredEmployees(filteredEmployees);
    } catch (error) {
        console.error('Error filtering employees:', error);
        showAlert('حدث خطأ أثناء البحث في الموظفين', 'error');
    }
}

// عرض الموظفين المفلترين
function displayFilteredEmployees(employees) {
    const tableBody = document.getElementById('employeesTableBody');

    if (employees.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center">
                    <div class="empty-state">
                        <i class="fas fa-search fa-3x"></i>
                        <h3>لا توجد نتائج</h3>
                        <p>لم يتم العثور على موظفين يطابقون معايير البحث</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    // استخدام نفس منطق العرض من loadEmployeesData
    tableBody.innerHTML = employees.map(employee => `
        <tr>
            <td>
                <div class="employee-photo">
                    ${employee.photo ?
                        `<img src="${employee.photo}" alt="${employee.name}">` :
                        `<div class="photo-placeholder"><i class="fas fa-user"></i></div>`
                    }
                </div>
            </td>
            <td><strong>${employee.name}</strong></td>
            <td>${employee.position}</td>
            <td>${employee.department}</td>
            <td>
                <span class="gender-badge ${employee.gender === 'ذكر' ? 'male' : 'female'}">
                    <i class="fas fa-${employee.gender === 'ذكر' ? 'mars' : 'venus'}"></i>
                    ${employee.gender}
                </span>
            </td>
            <td>${db.formatDate(employee.hireDate)}</td>
            <td><strong>${db.formatCurrency(employee.salary)}</strong></td>
            <td>
                <span class="children-count">
                    <i class="fas fa-child"></i>
                    ${employee.children ? employee.children.length : 0}
                </span>
            </td>
            <td>
                <span class="status-badge ${employee.status === 'نشط' ? 'active' : 'inactive'}">
                    ${employee.status || 'نشط'}
                </span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn-icon btn-primary" onclick="viewEmployee(${employee.id})" title="عرض">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn-icon btn-warning" onclick="editEmployee(${employee.id})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn-icon btn-danger" onclick="deleteEmployee(${employee.id})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('employeeSearch').value = '';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('genderFilter').value = '';
    loadEmployeesData();
}

// إضافة حقل طفل جديد
function addChildField() {
    const container = document.getElementById('childrenContainer');
    const childIndex = container.children.length;

    const childDiv = document.createElement('div');
    childDiv.className = 'child-form';
    childDiv.innerHTML = `
        <div class="form-grid">
            <div class="form-group">
                <label>اسم الطفل</label>
                <input type="text" name="childName_${childIndex}" placeholder="اسم الطفل">
            </div>
            <div class="form-group">
                <label>تاريخ الميلاد</label>
                <input type="date" name="childBirthDate_${childIndex}">
            </div>
            <div class="form-group">
                <label>المستوى التعليمي</label>
                <select name="childEducationLevel_${childIndex}">
                    <option value="">اختر المستوى</option>
                    <option value="ابتدائي">ابتدائي</option>
                    <option value="متوسط">متوسط</option>
                    <option value="ثانوي">ثانوي</option>
                    <option value="جامعي">جامعي</option>
                </select>
            </div>
            <div class="form-group">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeChildField(this)">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `;

    container.appendChild(childDiv);
}

// حذف حقل طفل
function removeChildField(button) {
    button.closest('.child-form').remove();
}

// حفظ موظف جديد
function saveEmployee() {
    if (!validateForm('addEmployeeForm')) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    const formData = new FormData(document.getElementById('addEmployeeForm'));
    const employee = {
        name: formData.get('name'),
        position: formData.get('position'),
        department: formData.get('department'),
        gender: formData.get('gender'),
        birthDate: formData.get('birthDate'),
        hireDate: formData.get('hireDate'),
        salary: parseFloat(formData.get('salary')),
        phone: formData.get('phone'),
        address: formData.get('address'),
        bankAccount: formData.get('bankAccount'),
        bankBranch: formData.get('bankBranch'),
        status: 'نشط',
        children: []
    };

    // جمع بيانات الأطفال
    const childrenContainer = document.getElementById('childrenContainer');
    const childForms = childrenContainer.querySelectorAll('.child-form');

    childForms.forEach((childForm, index) => {
        const childName = childForm.querySelector(`input[name="childName_${index}"]`).value;
        const childBirthDate = childForm.querySelector(`input[name="childBirthDate_${index}"]`).value;
        const childEducationLevel = childForm.querySelector(`select[name="childEducationLevel_${index}"]`).value;

        if (childName) {
            employee.children.push({
                name: childName,
                birthDate: childBirthDate,
                educationLevel: childEducationLevel
            });
        }
    });

    // معالجة الصورة
    const photoFile = document.getElementById('employeePhoto').files[0];
    if (photoFile) {
        const reader = new FileReader();
        reader.onload = function(e) {
            employee.photo = e.target.result;
            saveEmployeeToDatabase(employee);
        };
        reader.readAsDataURL(photoFile);
    } else {
        saveEmployeeToDatabase(employee);
    }
}

// حفظ الموظف في قاعدة البيانات
async function saveEmployeeToDatabase(employee) {
    try {
        // التحقق من رقم الحساب البنكي وتحويله إذا لزم الأمر
        const bankAccountValidation = validateAndFormatBankAccount(employee.bankAccount, employee.bankBranch);

        if (!bankAccountValidation.isValid) {
            showAlert(bankAccountValidation.message, 'error');
            return;
        }

        employee.bankAccount = bankAccountValidation.formattedAccount;

        await db.addEmployee(employee);

        // إظهار رسالة النجاح مع تفاصيل التحويل إذا لزم الأمر
        let successMessage = 'تم إضافة الموظف بنجاح';
        if (bankAccountValidation.message) {
            successMessage += '\n' + bankAccountValidation.message;
        }
        showAlert(successMessage, 'success');
        hideModal('addEmployeeModal');
        resetEmployeeForm();
        loadEmployeesData();
        loadEmployeeStatistics();
    } catch (error) {
        showAlert('حدث خطأ أثناء إضافة الموظف', 'error');
        console.error('Error adding employee:', error);
    }
}

// إعادة تعيين نموذج الموظف
function resetEmployeeForm() {
    document.getElementById('addEmployeeForm').reset();
    document.getElementById('childrenContainer').innerHTML = '';

    // إزالة رسائل التحقق وحالات الخطأ/النجاح
    removeValidationMessage();
    const bankAccountInput = document.getElementById('employeeBankAccount');
    if (bankAccountInput) {
        bankAccountInput.classList.remove('error', 'success');
    }
}

// عرض تفاصيل الموظف
function viewEmployee(employeeId) {
    const employee = db.getEmployees().find(emp => emp.id === employeeId);
    if (!employee) return;

    // إنشاء نافذة منبثقة لعرض التفاصيل
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user"></i> تفاصيل الموظف</h3>
                <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="employee-details">
                    <div class="employee-photo-large">
                        ${employee.photo ?
                            `<img src="${employee.photo}" alt="${employee.name}">` :
                            `<div class="photo-placeholder"><i class="fas fa-user"></i></div>`
                        }
                    </div>
                    <div class="employee-info">
                        <h2>${employee.name}</h2>
                        <p><strong>المنصب:</strong> ${employee.position}</p>
                        <p><strong>مكان العمل:</strong> ${employee.department}</p>
                        <p><strong>الجنس:</strong> ${employee.gender}</p>
                        <p><strong>تاريخ الميلاد:</strong> ${db.formatDate(employee.birthDate)}</p>
                        <p><strong>تاريخ التوظيف:</strong> ${db.formatDate(employee.hireDate)}</p>
                        <p><strong>الراتب:</strong> ${db.formatCurrency(employee.salary)}</p>
                        <p><strong>رقم الهاتف:</strong> ${employee.phone || 'غير محدد'}</p>
                        <p><strong>العنوان:</strong> ${employee.address || 'غير محدد'}</p>
                        <p><strong>رقم الحساب:</strong> ${employee.bankAccount || 'غير محدد'}</p>
                        <p><strong>الوكالة البنكية:</strong> ${employee.bankBranch || 'غير محدد'}</p>

                        ${employee.children && employee.children.length > 0 ? `
                            <div class="children-info">
                                <h4><i class="fas fa-child"></i> الأطفال</h4>
                                <ul>
                                    ${employee.children.map(child => `
                                        <li>
                                            <strong>${child.name}</strong> -
                                            ${db.formatDate(child.birthDate)} -
                                            ${child.educationLevel}
                                        </li>
                                    `).join('')}
                                </ul>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="this.closest('.modal').remove()">إغلاق</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    modal.style.display = 'block';
}

// تعديل الموظف
function editEmployee(employeeId) {
    const employee = db.getEmployees().find(emp => emp.id === employeeId);
    if (!employee) {
        showAlert('لم يتم العثور على الموظف', 'error');
        return;
    }

    // تحميل خيارات أماكن العمل والوكالات البنكية في نموذج التعديل
    loadEditWorkplaceOptions();
    loadEditBankBranchOptions();

    // ملء النموذج ببيانات الموظف
    populateEditForm(employee);

    // إعداد التحقق من رقم الحساب البنكي للتعديل
    setupEditBankAccountValidation();

    // إظهار النافذة
    showModal('editEmployeeModal');
}

// حذف الموظف
async function deleteEmployee(employeeId) {
    try {
        const employees = await db.getEmployees();
        const employee = employees.find(emp => emp.id === employeeId);
        if (!employee) {
            showAlert('لم يتم العثور على الموظف', 'error');
            return;
        }

        // التحقق من وجود قروض جارية للموظف
        const hasActiveLoans = await db.employeeHasActiveLoans(employeeId);

        if (hasActiveLoans) {
            showAlert(`لا يمكن حذف الموظف "${employee.name}" لأنه لديه قروض جارية التسديد.\n\nيجب تسديد جميع القروض أولاً قبل حذف الموظف.`, 'error');
            return;
        }

        if (confirm(`هل أنت متأكد من حذف الموظف "${employee.name}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
            await db.deleteEmployee(employeeId);
            showAlert('تم حذف الموظف بنجاح', 'success');
            loadEmployeesData();
            loadEmployeeStatistics();
        }
    } catch (error) {
        console.error('Error deleting employee:', error);

        // عرض رسالة خطأ مناسبة
        if (error.message.includes('قروض جارية')) {
            showAlert(error.message, 'error');
        } else {
            showAlert('حدث خطأ أثناء حذف الموظف: ' + error.message, 'error');
        }
    }
}

// استيراد الموظفين من Excel
function importEmployees() {
    showAlert('وظيفة الاستيراد قيد التطوير', 'info');
}

// تصدير بيانات الموظفين
function exportEmployees() {
    const employees = db.getEmployees();
    if (employees.length === 0) {
        showAlert('لا توجد بيانات للتصدير', 'error');
        return;
    }

    // تحضير البيانات للتصدير
    const exportData = employees.map(emp => ({
        'الاسم': emp.name,
        'المنصب': emp.position,
        'مكان العمل': emp.department,
        'الجنس': emp.gender,
        'تاريخ الميلاد': db.formatDate(emp.birthDate),
        'تاريخ التوظيف': db.formatDate(emp.hireDate),
        'الراتب': emp.salary,
        'رقم الهاتف': emp.phone || '',
        'العنوان': emp.address || '',
        'رقم الحساب': emp.bankAccount || '',
        'الوكالة البنكية': emp.bankBranch || '',
        'عدد الأطفال': emp.children ? emp.children.length : 0,
        'الحالة': emp.status || 'نشط'
    }));

    // إنشاء ملف CSV
    const csvContent = convertToCSV(exportData);
    downloadCSV(csvContent, 'employees_data.csv');

    showAlert('تم تصدير البيانات بنجاح', 'success');
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    const headers = Object.keys(data[0]);
    const csvRows = [headers.join(',')];

    data.forEach(row => {
        const values = headers.map(header => {
            const value = row[header];
            return `"${value}"`;
        });
        csvRows.push(values.join(','));
    });

    return csvRows.join('\n');
}

// تحميل ملف CSV
function downloadCSV(csvContent, filename) {
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');

    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// تحميل خيارات أماكن العمل
function loadWorkplaceOptions() {
    const workplaces = db.getWorkplaces();

    // تحديث فلتر أماكن العمل
    const departmentFilter = document.getElementById('departmentFilter');
    if (departmentFilter) {
        departmentFilter.innerHTML = '<option value="">جميع أماكن العمل</option>';
        workplaces.forEach(workplace => {
            const option = document.createElement('option');
            option.value = workplace;
            option.textContent = workplace;
            departmentFilter.appendChild(option);
        });
    }

    // تحديث قائمة أماكن العمل في نموذج إضافة الموظف
    const employeeDepartment = document.getElementById('employeeDepartment');
    if (employeeDepartment) {
        employeeDepartment.innerHTML = '<option value="">اختر مكان العمل</option>';
        workplaces.forEach(workplace => {
            const option = document.createElement('option');
            option.value = workplace;
            option.textContent = workplace;
            employeeDepartment.appendChild(option);
        });
    }
}

// تحميل قائمة أماكن العمل في نافذة الإدارة
function loadWorkplacesList() {
    const workplaces = db.getWorkplaces();
    const workplacesList = document.getElementById('workplacesList');

    if (!workplacesList) return;

    if (workplaces.length === 0) {
        workplacesList.innerHTML = `
            <div class="empty-state">
                <p>لا توجد أماكن عمل مسجلة</p>
            </div>
        `;
        return;
    }

    workplacesList.innerHTML = workplaces.map(workplace => `
        <div class="workplace-item">
            <span class="workplace-name">${workplace}</span>
            <div class="workplace-actions">
                <button class="btn-icon btn-warning btn-sm" onclick="editWorkplace('${workplace}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon btn-danger btn-sm" onclick="deleteWorkplace('${workplace}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// إضافة مكان عمل جديد
function addWorkplace() {
    const workplaceName = document.getElementById('newWorkplaceName').value.trim();

    if (!workplaceName) {
        showAlert('يرجى إدخال اسم مكان العمل', 'error');
        return;
    }

    if (workplaceName.length < 3) {
        showAlert('يجب أن يكون اسم مكان العمل 3 أحرف على الأقل', 'error');
        return;
    }

    const success = db.addWorkplace(workplaceName);

    if (success) {
        showAlert('تم إضافة مكان العمل بنجاح', 'success');
        document.getElementById('newWorkplaceName').value = '';
        loadWorkplacesList();
        loadWorkplaceOptions();
    } else {
        showAlert('مكان العمل موجود مسبقاً', 'error');
    }
}

// تعديل مكان عمل
function editWorkplace(workplaceName) {
    const newName = prompt('أدخل الاسم الجديد لمكان العمل:', workplaceName);

    if (newName && newName.trim() !== workplaceName) {
        const trimmedName = newName.trim();

        if (trimmedName.length < 3) {
            showAlert('يجب أن يكون اسم مكان العمل 3 أحرف على الأقل', 'error');
            return;
        }

        const success = db.updateWorkplace(workplaceName, trimmedName);

        if (success) {
            showAlert('تم تعديل مكان العمل بنجاح', 'success');
            loadWorkplacesList();
            loadWorkplaceOptions();
            loadEmployeesData(); // إعادة تحميل بيانات الموظفين لإظهار التغيير
        } else {
            showAlert('فشل في تعديل مكان العمل أو الاسم موجود مسبقاً', 'error');
        }
    }
}

// حذف مكان عمل
function deleteWorkplace(workplaceName) {
    if (confirm(`هل أنت متأكد من حذف مكان العمل "${workplaceName}"؟`)) {
        const result = db.deleteWorkplace(workplaceName);

        if (result.success) {
            showAlert('تم حذف مكان العمل بنجاح', 'success');
            loadWorkplacesList();
            loadWorkplaceOptions();
            loadEmployeesData();
        } else {
            showAlert(result.message, 'error');
        }
    }
}

// إظهار نافذة إدارة أماكن العمل
function showManageWorkplacesModal() {
    loadWorkplacesList();
    showModal('manageWorkplacesModal');
}

// تحديث showModal لتحميل قائمة أماكن العمل والوكالات البنكية عند فتح النافذة
const originalShowModal = window.showModal;
window.showModal = function(modalId) {
    if (modalId === 'manageWorkplacesModal') {
        loadWorkplacesList();
    } else if (modalId === 'manageBankBranchesModal') {
        loadBankBranchesList();
    }
    originalShowModal(modalId);
};

// إضافة مستمع للضغط على Enter في حقل إضافة مكان العمل
document.addEventListener('DOMContentLoaded', function() {
    const newWorkplaceInput = document.getElementById('newWorkplaceName');
    if (newWorkplaceInput) {
        newWorkplaceInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addWorkplace();
            }
        });
    }

    const newBankBranchInput = document.getElementById('newBankBranchName');
    if (newBankBranchInput) {
        newBankBranchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addBankBranch();
            }
        });
    }

    // إضافة التحقق من رقم الحساب البنكي
    setupBankAccountValidation();
});

// تحميل خيارات الوكالات البنكية
function loadBankBranchOptions() {
    const bankBranches = db.getBankBranches();

    // تحديث قائمة الوكالات البنكية في نموذج إضافة الموظف
    const employeeBankBranch = document.getElementById('employeeBankBranch');
    if (employeeBankBranch) {
        employeeBankBranch.innerHTML = '<option value="">اختر الوكالة البنكية</option>';
        bankBranches.forEach(branch => {
            const option = document.createElement('option');
            option.value = branch;
            option.textContent = branch;
            employeeBankBranch.appendChild(option);
        });
    }
}

// تحميل قائمة الوكالات البنكية في نافذة الإدارة
function loadBankBranchesList() {
    const bankBranches = db.getBankBranches();
    const bankBranchesList = document.getElementById('bankBranchesList');

    if (!bankBranchesList) return;

    if (bankBranches.length === 0) {
        bankBranchesList.innerHTML = `
            <div class="empty-state">
                <p>لا توجد وكالات بنكية مسجلة</p>
            </div>
        `;
        return;
    }

    bankBranchesList.innerHTML = bankBranches.map(branch => `
        <div class="bank-branch-item">
            <span class="bank-branch-name">${branch}</span>
            <div class="bank-branch-actions">
                <button class="btn-icon btn-warning btn-sm" onclick="editBankBranch('${branch.replace(/'/g, "\\\'")}')" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-icon btn-danger btn-sm" onclick="deleteBankBranch('${branch.replace(/'/g, "\\\'")}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>
    `).join('');
}

// إضافة وكالة بنكية جديدة
function addBankBranch() {
    const branchName = document.getElementById('newBankBranchName').value.trim();

    if (!branchName) {
        showAlert('يرجى إدخال اسم الوكالة البنكية', 'error');
        return;
    }

    if (branchName.length < 5) {
        showAlert('يجب أن يكون اسم الوكالة البنكية 5 أحرف على الأقل', 'error');
        return;
    }

    const success = db.addBankBranch(branchName);

    if (success) {
        showAlert('تم إضافة الوكالة البنكية بنجاح', 'success');
        document.getElementById('newBankBranchName').value = '';
        loadBankBranchesList();
        loadBankBranchOptions();
    } else {
        showAlert('الوكالة البنكية موجودة مسبقاً', 'error');
    }
}

// تعديل وكالة بنكية
function editBankBranch(branchName) {
    const newName = prompt('أدخل الاسم الجديد للوكالة البنكية:', branchName);

    if (newName && newName.trim() !== branchName) {
        const trimmedName = newName.trim();

        if (trimmedName.length < 5) {
            showAlert('يجب أن يكون اسم الوكالة البنكية 5 أحرف على الأقل', 'error');
            return;
        }

        const success = db.updateBankBranch(branchName, trimmedName);

        if (success) {
            showAlert('تم تعديل الوكالة البنكية بنجاح', 'success');
            loadBankBranchesList();
            loadBankBranchOptions();
            loadEmployeesData(); // إعادة تحميل بيانات الموظفين لإظهار التغيير
        } else {
            showAlert('فشل في تعديل الوكالة البنكية أو الاسم موجود مسبقاً', 'error');
        }
    }
}

// حذف وكالة بنكية
function deleteBankBranch(branchName) {
    if (confirm(`هل أنت متأكد من حذف الوكالة البنكية "${branchName}"؟`)) {
        const result = db.deleteBankBranch(branchName);

        if (result.success) {
            showAlert('تم حذف الوكالة البنكية بنجاح', 'success');
            loadBankBranchesList();
            loadBankBranchOptions();
            loadEmployeesData();
        } else {
            showAlert(result.message, 'error');
        }
    }
}

// التحقق من صحة رقم الحساب البنكي وتحويله
function validateAndFormatBankAccount(bankAccount, bankBranch) {
    // إذا لم يتم إدخال رقم حساب، فهو اختياري
    if (!bankAccount || bankAccount.trim() === '') {
        return { isValid: true, formattedAccount: '', message: '' };
    }

    // إزالة المسافات والرموز غير الرقمية
    const cleanAccount = bankAccount.replace(/\D/g, '');

    // التحقق من أن الرقم يحتوي على أرقام فقط
    if (cleanAccount !== bankAccount.replace(/\s/g, '')) {
        return {
            isValid: false,
            formattedAccount: '',
            message: 'رقم الحساب البنكي يجب أن يحتوي على أرقام فقط'
        };
    }

    // التحقق من طول الرقم
    if (cleanAccount.length === 0) {
        return { isValid: true, formattedAccount: '', message: '' };
    }

    // التحقق إذا كانت الوكالة من بريد الجزائر
    const isPostalAccount = bankBranch && bankBranch.toLowerCase().includes('بريد الجزائر');

    if (isPostalAccount) {
        // للحسابات البريدية
        if (cleanAccount.length < 10) {
            return {
                isValid: false,
                formattedAccount: '',
                message: 'رقم الحساب البريدي يجب أن يكون 10 أرقام على الأقل'
            };
        }

        if (cleanAccount.length < 20) {
            // تحويل إلى RIP
            const ripAccount = convertToRIP(cleanAccount);
            return {
                isValid: true,
                formattedAccount: ripAccount,
                message: `تم تحويل الرقم إلى RIP: ${ripAccount}`
            };
        } else if (cleanAccount.length === 20) {
            // الرقم صحيح كما هو
            return {
                isValid: true,
                formattedAccount: cleanAccount,
                message: ''
            };
        } else {
            return {
                isValid: false,
                formattedAccount: '',
                message: 'رقم الحساب البريدي لا يمكن أن يزيد عن 20 رقم'
            };
        }
    } else {
        // للحسابات البنكية العادية
        if (cleanAccount.length !== 20) {
            return {
                isValid: false,
                formattedAccount: '',
                message: 'رقم الحساب البنكي يجب أن يكون مكون من 20 رقم بالضبط'
            };
        }

        return {
            isValid: true,
            formattedAccount: cleanAccount,
            message: ''
        };
    }
}

// تحويل رقم الحساب البريدي إلى RIP
function convertToRIP(accountNumber) {
    // إضافة الصفر في البداية إذا لزم الأمر لجعل الرقم 12 رقم
    let paddedAccount = accountNumber.padStart(12, '0');

    // إضافة رمز البلد (12 للجزائر) ورقم التحقق
    const countryCode = '12';
    const bankCode = '00020'; // رمز بريد الجزائر

    // تكوين الرقم الأساسي
    let baseNumber = bankCode + paddedAccount + countryCode + '00';

    // حساب رقم التحقق
    const checkDigits = calculateIBANCheckDigits(baseNumber);

    // تكوين الرقم النهائي
    const ripNumber = countryCode + checkDigits + bankCode + paddedAccount;

    return ripNumber;
}

// حساب أرقام التحقق لـ IBAN
function calculateIBANCheckDigits(accountString) {
    // تحويل الأحرف إلى أرقام (A=10, B=11, etc.)
    let numericString = '';
    for (let i = 0; i < accountString.length; i++) {
        const char = accountString[i];
        if (char >= '0' && char <= '9') {
            numericString += char;
        } else {
            numericString += (char.charCodeAt(0) - 55).toString();
        }
    }

    // حساب باقي القسمة على 97
    let remainder = 0;
    for (let i = 0; i < numericString.length; i++) {
        remainder = (remainder * 10 + parseInt(numericString[i])) % 97;
    }

    // حساب رقم التحقق
    const checkDigits = 98 - remainder;

    // إرجاع الرقم مع إضافة صفر في البداية إذا لزم الأمر
    return checkDigits.toString().padStart(2, '0');
}

// إضافة مستمع للتحقق من رقم الحساب أثناء الكتابة
function setupBankAccountValidation() {
    const bankAccountInput = document.getElementById('employeeBankAccount');
    const bankBranchSelect = document.getElementById('employeeBankBranch');

    if (bankAccountInput) {
        bankAccountInput.addEventListener('input', function() {
            validateBankAccountInput();
        });

        bankAccountInput.addEventListener('blur', function() {
            validateBankAccountInput();
        });
    }

    if (bankBranchSelect) {
        bankBranchSelect.addEventListener('change', function() {
            validateBankAccountInput();
        });
    }
}

// التحقق من رقم الحساب أثناء الكتابة
function validateBankAccountInput() {
    const bankAccountInput = document.getElementById('employeeBankAccount');
    const bankBranchSelect = document.getElementById('employeeBankBranch');

    if (!bankAccountInput) return;

    const bankAccount = bankAccountInput.value;
    const bankBranch = bankBranchSelect ? bankBranchSelect.value : '';

    // إزالة الرسائل السابقة
    removeValidationMessage();

    if (bankAccount.trim() === '') {
        bankAccountInput.classList.remove('error', 'success');
        return;
    }

    const validation = validateAndFormatBankAccount(bankAccount, bankBranch);

    if (validation.isValid) {
        bankAccountInput.classList.remove('error');
        bankAccountInput.classList.add('success');

        if (validation.message) {
            showValidationMessage(validation.message, 'success');
        }
    } else {
        bankAccountInput.classList.remove('success');
        bankAccountInput.classList.add('error');
        showValidationMessage(validation.message, 'error');
    }
}

// إظهار رسالة التحقق
function showValidationMessage(message, type) {
    const bankAccountInput = document.getElementById('employeeBankAccount');
    if (!bankAccountInput) return;

    // إزالة الرسالة السابقة
    removeValidationMessage();

    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `validation-message ${type}`;
    messageDiv.textContent = message;
    messageDiv.id = 'bankAccountValidationMessage';

    // إضافة الرسالة بعد حقل الإدخال
    bankAccountInput.parentNode.appendChild(messageDiv);
}

// إزالة رسالة التحقق
function removeValidationMessage() {
    const existingMessage = document.getElementById('bankAccountValidationMessage');
    if (existingMessage) {
        existingMessage.remove();
    }
}

// تحميل خيارات أماكن العمل في نموذج التعديل
function loadEditWorkplaceOptions() {
    const workplaces = db.getWorkplaces();
    const editEmployeeDepartment = document.getElementById('editEmployeeDepartment');

    if (editEmployeeDepartment) {
        editEmployeeDepartment.innerHTML = '<option value="">اختر مكان العمل</option>';
        workplaces.forEach(workplace => {
            const option = document.createElement('option');
            option.value = workplace;
            option.textContent = workplace;
            editEmployeeDepartment.appendChild(option);
        });
    }
}

// تحميل خيارات الوكالات البنكية في نموذج التعديل
function loadEditBankBranchOptions() {
    const bankBranches = db.getBankBranches();
    const editEmployeeBankBranch = document.getElementById('editEmployeeBankBranch');

    if (editEmployeeBankBranch) {
        editEmployeeBankBranch.innerHTML = '<option value="">اختر الوكالة البنكية</option>';
        bankBranches.forEach(branch => {
            const option = document.createElement('option');
            option.value = branch;
            option.textContent = branch;
            editEmployeeBankBranch.appendChild(option);
        });
    }
}

// ملء نموذج التعديل ببيانات الموظف
function populateEditForm(employee) {
    // حفظ معرف الموظف للاستخدام عند التحديث
    document.getElementById('editEmployeeForm').dataset.employeeId = employee.id;

    // ملء الحقول الأساسية
    document.getElementById('editEmployeeName').value = employee.name || '';
    document.getElementById('editEmployeePosition').value = employee.position || '';
    document.getElementById('editEmployeeDepartment').value = employee.department || '';
    document.getElementById('editEmployeeGender').value = employee.gender || '';
    document.getElementById('editEmployeeBirthDate').value = employee.birthDate || '';
    document.getElementById('editEmployeeHireDate').value = employee.hireDate || '';
    document.getElementById('editEmployeeSalary').value = employee.salary || '';
    document.getElementById('editEmployeePhone').value = employee.phone || '';
    document.getElementById('editEmployeeAddress').value = employee.address || '';
    document.getElementById('editEmployeeBankAccount').value = employee.bankAccount || '';
    document.getElementById('editEmployeeBankBranch').value = employee.bankBranch || '';
    document.getElementById('editEmployeeStatus').value = employee.status || 'نشط';

    // عرض الصورة الحالية إذا كانت موجودة
    const currentPhotoPreview = document.getElementById('currentPhotoPreview');
    const currentPhoto = document.getElementById('currentPhoto');

    if (employee.photo) {
        currentPhoto.src = employee.photo;
        currentPhotoPreview.style.display = 'block';
    } else {
        currentPhotoPreview.style.display = 'none';
    }

    // ملء بيانات الأطفال
    populateEditChildrenForm(employee.children || []);
}

// ملء نموذج الأطفال في التعديل
function populateEditChildrenForm(children) {
    const container = document.getElementById('editChildrenContainer');
    container.innerHTML = '';

    children.forEach((child, index) => {
        const childDiv = document.createElement('div');
        childDiv.className = 'child-form';
        childDiv.innerHTML = `
            <div class="form-grid">
                <div class="form-group">
                    <label>اسم الطفل</label>
                    <input type="text" name="editChildName_${index}" placeholder="اسم الطفل" value="${child.name || ''}">
                </div>
                <div class="form-group">
                    <label>تاريخ الميلاد</label>
                    <input type="date" name="editChildBirthDate_${index}" value="${child.birthDate || ''}">
                </div>
                <div class="form-group">
                    <label>المستوى التعليمي</label>
                    <select name="editChildEducationLevel_${index}">
                        <option value="">اختر المستوى</option>
                        <option value="ابتدائي" ${child.educationLevel === 'ابتدائي' ? 'selected' : ''}>ابتدائي</option>
                        <option value="متوسط" ${child.educationLevel === 'متوسط' ? 'selected' : ''}>متوسط</option>
                        <option value="ثانوي" ${child.educationLevel === 'ثانوي' ? 'selected' : ''}>ثانوي</option>
                        <option value="جامعي" ${child.educationLevel === 'جامعي' ? 'selected' : ''}>جامعي</option>
                    </select>
                </div>
                <div class="form-group">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeEditChildField(this)">
                        <i class="fas fa-trash"></i> حذف
                    </button>
                </div>
            </div>
        `;
        container.appendChild(childDiv);
    });
}

// إضافة حقل طفل جديد في نموذج التعديل
function addEditChildField() {
    const container = document.getElementById('editChildrenContainer');
    const childIndex = container.children.length;

    const childDiv = document.createElement('div');
    childDiv.className = 'child-form';
    childDiv.innerHTML = `
        <div class="form-grid">
            <div class="form-group">
                <label>اسم الطفل</label>
                <input type="text" name="editChildName_${childIndex}" placeholder="اسم الطفل">
            </div>
            <div class="form-group">
                <label>تاريخ الميلاد</label>
                <input type="date" name="editChildBirthDate_${childIndex}">
            </div>
            <div class="form-group">
                <label>المستوى التعليمي</label>
                <select name="editChildEducationLevel_${childIndex}">
                    <option value="">اختر المستوى</option>
                    <option value="ابتدائي">ابتدائي</option>
                    <option value="متوسط">متوسط</option>
                    <option value="ثانوي">ثانوي</option>
                    <option value="جامعي">جامعي</option>
                </select>
            </div>
            <div class="form-group">
                <button type="button" class="btn btn-danger btn-sm" onclick="removeEditChildField(this)">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `;

    container.appendChild(childDiv);
}

// حذف حقل طفل في نموذج التعديل
function removeEditChildField(button) {
    button.closest('.child-form').remove();
}

// إعداد التحقق من رقم الحساب البنكي للتعديل
function setupEditBankAccountValidation() {
    const bankAccountInput = document.getElementById('editEmployeeBankAccount');
    const bankBranchSelect = document.getElementById('editEmployeeBankBranch');

    if (bankAccountInput) {
        bankAccountInput.addEventListener('input', function() {
            validateEditBankAccountInput();
        });

        bankAccountInput.addEventListener('blur', function() {
            validateEditBankAccountInput();
        });
    }

    if (bankBranchSelect) {
        bankBranchSelect.addEventListener('change', function() {
            validateEditBankAccountInput();
        });
    }
}

// التحقق من رقم الحساب أثناء الكتابة في نموذج التعديل
function validateEditBankAccountInput() {
    const bankAccountInput = document.getElementById('editEmployeeBankAccount');
    const bankBranchSelect = document.getElementById('editEmployeeBankBranch');

    if (!bankAccountInput) return;

    const bankAccount = bankAccountInput.value;
    const bankBranch = bankBranchSelect ? bankBranchSelect.value : '';

    // إزالة الرسائل السابقة
    removeEditValidationMessage();

    if (bankAccount.trim() === '') {
        bankAccountInput.classList.remove('error', 'success');
        return;
    }

    const validation = validateAndFormatBankAccount(bankAccount, bankBranch);

    if (validation.isValid) {
        bankAccountInput.classList.remove('error');
        bankAccountInput.classList.add('success');

        if (validation.message) {
            showEditValidationMessage(validation.message, 'success');
        }
    } else {
        bankAccountInput.classList.remove('success');
        bankAccountInput.classList.add('error');
        showEditValidationMessage(validation.message, 'error');
    }
}

// إظهار رسالة التحقق في نموذج التعديل
function showEditValidationMessage(message, type) {
    const bankAccountInput = document.getElementById('editEmployeeBankAccount');
    if (!bankAccountInput) return;

    // إزالة الرسالة السابقة
    removeEditValidationMessage();

    // إنشاء رسالة جديدة
    const messageDiv = document.createElement('div');
    messageDiv.className = `validation-message ${type}`;
    messageDiv.textContent = message;
    messageDiv.id = 'editBankAccountValidationMessage';

    // إضافة الرسالة بعد حقل الإدخال
    bankAccountInput.parentNode.appendChild(messageDiv);
}

// إزالة رسالة التحقق في نموذج التعديل
function removeEditValidationMessage() {
    const existingMessage = document.getElementById('editBankAccountValidationMessage');
    if (existingMessage) {
        existingMessage.remove();
    }
}

// تحديث بيانات الموظف
function updateEmployee() {
    const form = document.getElementById('editEmployeeForm');
    const employeeId = parseInt(form.dataset.employeeId);

    if (!employeeId) {
        showAlert('خطأ في تحديد الموظف', 'error');
        return;
    }

    // جمع البيانات من النموذج
    const formData = new FormData();

    // البيانات الأساسية
    const name = document.getElementById('editEmployeeName').value.trim();
    const position = document.getElementById('editEmployeePosition').value.trim();
    const department = document.getElementById('editEmployeeDepartment').value;
    const gender = document.getElementById('editEmployeeGender').value;
    const birthDate = document.getElementById('editEmployeeBirthDate').value;
    const hireDate = document.getElementById('editEmployeeHireDate').value;
    const salary = document.getElementById('editEmployeeSalary').value;
    const phone = document.getElementById('editEmployeePhone').value.trim();
    const address = document.getElementById('editEmployeeAddress').value.trim();
    const bankAccount = document.getElementById('editEmployeeBankAccount').value.trim();
    const bankBranch = document.getElementById('editEmployeeBankBranch').value;
    const status = document.getElementById('editEmployeeStatus').value;

    // التحقق من الحقول المطلوبة
    if (!name || !position || !department || !gender || !birthDate || !hireDate || !salary) {
        showAlert('يرجى ملء جميع الحقول المطلوبة', 'error');
        return;
    }

    // التحقق من صحة رقم الحساب البنكي
    let bankAccountValidation = { isValid: true, formattedAccount: bankAccount, message: '' };
    if (bankAccount) {
        bankAccountValidation = validateAndFormatBankAccount(bankAccount, bankBranch);
        if (!bankAccountValidation.isValid) {
            showAlert(bankAccountValidation.message, 'error');
            return;
        }
    }

    // جمع بيانات الأطفال
    const children = collectEditChildrenData();

    // معالجة الصورة
    const photoFile = document.getElementById('editEmployeePhoto').files[0];
    let photoPromise = Promise.resolve(null);

    if (photoFile) {
        photoPromise = new Promise((resolve) => {
            const reader = new FileReader();
            reader.onload = function(e) {
                resolve(e.target.result);
            };
            reader.readAsDataURL(photoFile);
        });
    }

    photoPromise.then(photoData => {
        // إنشاء كائن الموظف المحدث
        const updatedEmployee = {
            id: employeeId,
            name,
            position,
            department,
            gender,
            birthDate,
            hireDate,
            salary: parseFloat(salary),
            phone,
            address,
            bankAccount: bankAccountValidation.formattedAccount,
            bankBranch,
            status,
            children,
            photo: photoData || db.getEmployees().find(emp => emp.id === employeeId)?.photo || null
        };

        // تحديث الموظف في قاعدة البيانات
        const result = db.updateEmployee(employeeId, updatedEmployee);

        if (result.success) {
            // إظهار رسالة النجاح مع تفاصيل التحويل إذا لزم الأمر
            let successMessage = 'تم تحديث بيانات الموظف بنجاح';
            if (bankAccountValidation.message) {
                successMessage += '\n' + bankAccountValidation.message;
            }
            showAlert(successMessage, 'success');

            // إخفاء النافذة وتحديث الجدول
            hideModal('editEmployeeModal');
            displayEmployees();

            // إعادة تعيين النموذج
            resetEditEmployeeForm();
        } else {
            showAlert(result.message, 'error');
        }
    });
}

// جمع بيانات الأطفال من نموذج التعديل
function collectEditChildrenData() {
    const children = [];
    const container = document.getElementById('editChildrenContainer');
    const childForms = container.querySelectorAll('.child-form');

    childForms.forEach((childForm, index) => {
        const nameInput = childForm.querySelector(`input[name="editChildName_${index}"]`);
        const birthDateInput = childForm.querySelector(`input[name="editChildBirthDate_${index}"]`);
        const educationLevelSelect = childForm.querySelector(`select[name="editChildEducationLevel_${index}"]`);

        const name = nameInput ? nameInput.value.trim() : '';
        const birthDate = birthDateInput ? birthDateInput.value : '';
        const educationLevel = educationLevelSelect ? educationLevelSelect.value : '';

        if (name || birthDate || educationLevel) {
            children.push({
                name,
                birthDate,
                educationLevel
            });
        }
    });

    return children;
}

// إعادة تعيين نموذج التعديل
function resetEditEmployeeForm() {
    document.getElementById('editEmployeeForm').reset();
    document.getElementById('editChildrenContainer').innerHTML = '';
    document.getElementById('currentPhotoPreview').style.display = 'none';

    // إزالة رسائل التحقق وحالات الخطأ/النجاح
    removeEditValidationMessage();
    const bankAccountInput = document.getElementById('editEmployeeBankAccount');
    if (bankAccountInput) {
        bankAccountInput.classList.remove('error', 'success');
    }
}

